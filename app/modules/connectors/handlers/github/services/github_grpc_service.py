import grpc
import structlog
from typing import Dict, Any, List
from datetime import datetime

from app.grpc_ import connector_pb2, connector_pb2_grpc
from app.modules.connectors.handlers.github.services.github_service import GitHubService
from app.modules.connectors.handlers.github.workers.sync_worker import GitHubSyncWorker
from app.modules.organisation.services.organisation import OrganisationService

logger = structlog.get_logger()


class GitHubGrpcService(connector_pb2_grpc.ConnectorServiceServicer):
    """
    gRPC service for GitHub connector operations.
    """

    def __init__(self):
        self.github_service = GitHubService()
        self.sync_worker = GitHubSyncWorker()
        self.organisation_service = OrganisationService()

    def SyncConnector(self, request, context):
        """
        Schedule GitHub sync to run in background and return immediately.
        """
        logger.info("Received request to sync GitHub",
                   organisation_id=request.organisation_id,
                   full_sync=request.full_sync)

        try:
            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return connector_pb2.SyncResponse(
                    success=False,
                    message="organisation_id is required",
                    items_synced=0,
                    sync_time=datetime.now().isoformat()
                )

            # Schedule sync job in background instead of running synchronously
            job_id = self.github_service._schedule_sync(
                user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                organisation_id=request.organisation_id,
                full_sync=request.full_sync,
                delay_seconds=0  # Start immediately
            )

            return connector_pb2.SyncResponse(
                success=True,
                message=f"GitHub sync scheduled successfully. Job ID: {job_id}",
                items_synced=0,  # Will be updated when job completes
                sync_time=datetime.now().isoformat()
            )

        except ValueError as e:
            logger.error("Invalid parameters for GitHub sync", error=str(e))
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
            return connector_pb2.SyncResponse(
                success=False,
                message=str(e),
                items_synced=0,
                sync_time=datetime.now().isoformat()
            )
        except Exception as e:
            logger.error("Error scheduling GitHub sync", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error scheduling GitHub sync: {str(e)}")
            return connector_pb2.SyncResponse(
                success=False,
                message=f"Error scheduling sync: {str(e)}",
                items_synced=0,
                sync_time=datetime.now().isoformat()
            )

    def SyncByUrl(self, request, context):
        """
        Sync a specific GitHub repository by URL.
        """
        logger.info("Received request to sync GitHub repository by URL",
                   url=request.url,
                   agent_id=request.agent_id,
                   user_id=request.user_id if hasattr(request, 'user_id') and request.user_id else "None",
                   organisation_id=request.organisation_id)

        try:
            # Validate that we have a URL to process
            if not request.url:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("No URL provided")
                return connector_pb2.SyncByUrlResponse(
                    success=False,
                    message="No URL provided",
                    items_synced=0,
                    sync_time=datetime.now().isoformat()
                )

            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return connector_pb2.SyncByUrlResponse(
                    success=False,
                    message="organisation_id is required",
                    items_synced=0,
                    sync_time=datetime.now().isoformat()
                )

            # Schedule repository sync job in background
            job_id = self.sync_worker.schedule_sync(
                organisation_id=request.organisation_id,
                sync_type='repository_sync',
                delay_minutes=0,  # Start immediately
                github_url=request.url,
                agent_id=request.agent_id,
                user_id=request.user_id if request.user_id else None,
                full_sync=request.full_sync
            )

            return connector_pb2.SyncByUrlResponse(
                success=True,
                message=f"GitHub repository sync scheduled successfully. Job ID: {job_id}",
                items_synced=0,  # Will be updated when job completes
                sync_time=datetime.now().isoformat()
            )

        except ValueError as e:
            logger.error("Invalid parameters for GitHub URL sync", error=str(e))
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
            return connector_pb2.SyncByUrlResponse(
                success=False,
                message=str(e),
                items_synced=0,
                sync_time=datetime.now().isoformat()
            )
        except Exception as e:
            logger.error("Error scheduling GitHub URL sync", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"GitHub URL sync failed: {str(e)}")
            return connector_pb2.SyncByUrlResponse(
                success=False,
                message=f"URL sync failed: {str(e)}",
                items_synced=0,
                sync_time=datetime.now().isoformat()
            )

    def SearchConnector(self, request, context):
        """
        Search GitHub content.
        """
        try:
            logger.info(f"GitHub search requested: {request.query}")
            
            # Perform search
            results = self.github_service.search_github_content(
                organisation_id=request.organisation_id,
                query=request.query,
                limit=request.limit if request.limit > 0 else 10
            )
            
            # Convert results to protobuf format
            search_results = []
            for result in results:
                search_result = connector_pb2.SearchResult(
                    id=str(result.get('id', '')),
                    title=result.get('title', ''),
                    content=result.get('content', ''),
                    url=result.get('url', ''),
                    type=result.get('type', ''),
                    score=result.get('score', 0.0),
                    metadata=str(result.get('metadata', {}))
                )
                search_results.append(search_result)
            
            return connector_pb2.SearchResponse(
                success=True,
                results=search_results,
                total_count=len(search_results),
                message="Search completed successfully"
            )
            
        except Exception as e:
            logger.error(f"Error in GitHub search gRPC call: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"GitHub search failed: {str(e)}")
            return connector_pb2.SearchResponse(
                success=False,
                results=[],
                total_count=0,
                message=f"Search failed: {str(e)}"
            )

    def GetConnectorInfo(self, request, context):
        """
        Get GitHub connector information.
        """
        try:
            connector_info = {
                "name": "GitHub",
                "version": "1.0.0",
                "type": "code_repository",
                "description": "GitHub connector for repositories, issues, pull requests, and code",
                "capabilities": [
                    "repositories",
                    "issues", 
                    "pull_requests",
                    "commits",
                    "code_search",
                    "organizations",
                    "users"
                ],
                "authentication": {
                    "type": "personal_access_token",
                    "required_fields": ["token"]
                }
            }
            
            return connector_pb2.ConnectorInfoResponse(
                success=True,
                info=str(connector_info),
                message="Connector info retrieved successfully"
            )
            
        except Exception as e:
            logger.error(f"Error getting GitHub connector info: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get connector info: {str(e)}")
            return connector_pb2.ConnectorInfoResponse(
                success=False,
                info="{}",
                message=f"Failed to get connector info: {str(e)}"
            )

    def GetSyncStatus(self, request, context):
        """
        Get GitHub sync status and statistics.
        """
        logger.info("Received request to get GitHub sync status",
                   organisation_id=request.organisation_id)

        try:
            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return connector_pb2.SyncStatusResponse(
                    success=False,
                    last_sync_time="",
                    items_count=0,
                    status="error",
                    message="organisation_id is required",
                    metadata="{}"
                )

            # Get sync statistics
            stats = self.github_service.get_sync_statistics(request.organisation_id)

            return connector_pb2.SyncStatusResponse(
                success=True,
                last_sync_time=stats.get('last_sync_time', ''),
                items_count=stats.get('repositories_count', 0) +
                           stats.get('issues_count', 0) +
                           stats.get('pull_requests_count', 0) +
                           stats.get('commits_count', 0),
                status="active",
                message="Sync status retrieved successfully",
                metadata=str(stats)
            )

        except Exception as e:
            logger.error("Error getting GitHub sync status", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get sync status: {str(e)}")
            return connector_pb2.SyncStatusResponse(
                success=False,
                last_sync_time="",
                items_count=0,
                status="error",
                message=f"Failed to get sync status: {str(e)}",
                metadata="{}"
            )

    def TestConnection(self, request, context):
        """
        Test GitHub connection.
        """
        logger.info("Received request to test GitHub connection",
                    organisation_id=request.organisation_id)

        try:
            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return connector_pb2.TestConnectionResponse(
                    success=False,
                    message="organisation_id is required"
                )

            # Test connection by attempting to fetch user info
            from app.utils.source_credentials import get_source_credentials
            import requests

            credentials = get_source_credentials(request.organisation_id, 'github')
            if not credentials:
                return connector_pb2.TestConnectionResponse(
                    success=False,
                    message="No GitHub credentials found for organisation"
                )

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return connector_pb2.TestConnectionResponse(
                    success=False,
                    message="GitHub token not found in credentials"
                )

            # Test API call
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })

            response = session.get('https://api.github.com/user')
            response.raise_for_status()

            user_data = response.json()

            return connector_pb2.TestConnectionResponse(
                success=True,
                message=f"Connection successful. Authenticated as: {user_data.get('login', 'Unknown')}"
            )

        except Exception as e:
            logger.error("Error testing GitHub connection", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Connection test failed: {str(e)}")
            return connector_pb2.TestConnectionResponse(
                success=False,
                message=f"Connection test failed: {str(e)}"
            )

    def listTopLevelRepositories(self, request, context):
        """
        List top-level repositories from GitHub.
        """
        logger.info("Received request to list top-level repositories",
                    organisation_id=request.organisation_id)
        
        try:
            success, message, repositories = self.github_service.fetch_top_level_repositories(
                request.organisation_id
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return connector_pb2.ListTopLevelRepositoriesResponse(success=False)
            
            # Convert repositories to proto models (assuming proto has RepositoryInfo)
            repo_models = []
            for repo in repositories:
                repo_model = connector_pb2.RepositoryInfo(
                    id=repo['id'],
                    name=repo['name']
                )
                repo_models.append(repo_model)
            
            return connector_pb2.ListTopLevelRepositoriesResponse(
                success=True,
                message=message,
                repositories=repo_models
            )
            
        except Exception as e:
            logger.error("Error listing top-level repositories", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing repositories: {str(e)}")
            return connector_pb2.ListTopLevelRepositoriesResponse(success=False)

    def disconnectGitHub(self, request, context):
        """
        Disconnect GitHub for an organization (remove synced data).
        """
        logger.info("Received request to disconnect GitHub",
                    organisation_id=request.organisation_id)
        
        try:
            success, message = self.github_service.disconnect_github(request.organisation_id)
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return connector_pb2.DisconnectResponse(success=False)
            
            return connector_pb2.DisconnectResponse(
                success=True,
                message=message
            )
            
        except Exception as e:
            logger.error("Error disconnecting GitHub", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error disconnecting GitHub: {str(e)}")
            return connector_pb2.DisconnectResponse(success=False)

    def listRepositoryFiles(self, request, context):
        """
        List files in a GitHub repository.
        """
        logger.info("Received request to list GitHub repository files",
                    user_id=request.user_id, repo_id=request.repo_id)
        
        try:
            files, total_count, page, page_size = self.github_service.list_repository_files(
                request.user_id,
                request.repo_id if request.repo_id else None,
                request.page if request.page > 0 else 1,
                request.page_size if request.page_size > 0 else 50
            )
            
            # Convert to proto models (assuming FileModel)
            file_models = []
            for file_data in files:
                file_model = connector_pb2.FileModel(
                    id=file_data['id'],
                    name=file_data['name'],
                    path=file_data['path'],
                    url=file_data['url'],
                    size=int(file_data.get('size', 0)),
                    last_modified=file_data['last_modified']
                )
                file_models.append(file_model)
            
            return connector_pb2.ListFilesResponse(
                success=True,
                message="Files retrieved successfully",
                files=file_models,
                total_count=total_count,
                page=page,
                page_size=page_size
            )
            
        except Exception as e:
            logger.error("Error listing GitHub files", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing files: {str(e)}")
            return connector_pb2.ListFilesResponse(success=False)

    def getRepositoryDetails(self, request, context):
        """
        Get repository details.
        """
        logger.info("Received request to get GitHub repository details",
                    user_id=request.user_id, repo_id=request.repo_id)
        
        try:
            success, message, repo_data = self.github_service.get_repository_details(
                request.user_id,
                request.repo_id
            )
            
            if not success or not repo_data:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return connector_pb2.GetRepositoryDetailsResponse(success=False)
            
            # Convert to proto model (assuming RepositoryModel)
            repo_model = connector_pb2.RepositoryModel(
                id=repo_data['id'],
                name=repo_data['name'],
                url=repo_data['url'],
                description=repo_data['description'],
                stars=repo_data.get('stars', 0),
                forks=repo_data.get('forks', 0),
                issues=repo_data.get('issues', 0),
                created_time=repo_data['created_time'],
                updated_time=repo_data['updated_time']
            )
            
            return connector_pb2.GetRepositoryDetailsResponse(
                success=True,
                message=message,
                repository=repo_model
            )
            
        except Exception as e:
            logger.error("Error getting GitHub repository details", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting repository details: {str(e)}")
            return connector_pb2.GetRepositoryDetailsResponse(success=False)

    def syncRepositoryByIds(self, request, context):
        """
        Sync specific repositories by IDs.
        """
        logger.info("Received request to sync GitHub repositories by IDs",
                    organisation_id=request.organisation_id, repo_ids=request.repo_ids)
        
        try:
            job_id = self.sync_worker.schedule_repository_sync_job(
                organisation_id=request.organisation_id,
                repo_ids=list(request.repo_ids),
                delay_seconds=0
            )
            
            return connector_pb2.SyncRepositoryByIdsResponse(
                success=True,
                message=f"Repository sync job scheduled. Job ID: {job_id}",
                synced_repos=[]
            )
            
        except Exception as e:
            logger.error("Error scheduling repository sync", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error: {str(e)}")
            return connector_pb2.SyncRepositoryByIdsResponse(success=False)

    def checkRepositoryAccess(self, request, context):
        """
        Check if a user has access to a repository.
        """
        logger.info("Received request to check repository access",
                    user_id=request.user_id, repo_id=request.repo_id)
        
        try:
            has_access = self.github_service.check_repository_access(
                request.user_id,
                request.repo_id
            )
            
            return connector_pb2.CheckAccessResponse(
                success=True,
                has_access=has_access
            )
            
        except Exception as e:
            logger.error("Error checking access", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error: {str(e)}")
            return connector_pb2.CheckAccessResponse(success=False, has_access=False)

    def batchSearchSimilarDocuments(self, request, context):
        """
        Batch search for similar documents in GitHub.
        """
        logger.info("Received batch search request")
        
        try:
            results = self.github_service.batch_search_similar_documents(
                request.user_id,
                list(request.queries),
                request.top_k
            )
            
            batch_results = []
            for result in results:
                batch_results.append(connector_pb2.SearchResultList(results=result))
            
            return connector_pb2.BatchSearchResponse(
                success=True,
                results=batch_results
            )
            
        except Exception as e:
            logger.error("Error in batch search", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error: {str(e)}")
            return connector_pb2.BatchSearchResponse(success=False)
