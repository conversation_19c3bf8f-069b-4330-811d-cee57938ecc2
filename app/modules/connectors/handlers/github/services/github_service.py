import j<PERSON>
import uuid
from datetime import datetime
import structlog
from typing import <PERSON><PERSON>, List, Dict, Any, Optional
import requests
import time
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
import base64

from app.services.neo4j_service import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.utils.source_credentials import get_source_credentials
from app.modules.organisation.repository.source import SourceQueries
from app.utils.constants.sources import SourceType
from app.modules.connectors.handlers.github.repository.github_queries import (
    GitHubUserQueries,
    GitHubOrganizationQueries,
    GitHubRepositoryQueries,
    GitHubIssueQueries,
    GitHubPullRequestQueries,
    GitHubCommitQueries,
    GitHubFileQueries,
    GitHubTeamQueries,
    GitHubTagQueries,
    GitHubReleaseQueries,
    GitHubCommentQueries,
    GitHubReviewQueries,
    GitHubWorkflowQueries,
    GitHubRelationshipQueries,
    GitHubSyncQueries
)
from app.utils.file_processing.text_extractor import TextExtractor
from app.utils.knowledge_graph.chunking_engine import ChunkingEngine

logger = structlog.get_logger()


class GitHubAPIClient:
    """
    Dedicated GitHub API client for handling all API interactions.
    Provides clean methods for all GitHub API endpoints with proper error handling,
    rate limiting, and retry logic.
    """

    def __init__(self, token: str):
        self.token = token
        self.session = self._create_robust_session()
        self.rate_limit_remaining = 5000
        self.rate_limit_reset_time = None
        self.max_retries = 3
        self.retry_delay = 1

    def _create_robust_session(self) -> requests.Session:
        """Create a robust session with retry strategy and rate limiting."""
        session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=self.max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS"],
            backoff_factor=2
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set headers
        session.headers.update({
            'Authorization': f'token {self.token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'RuhOrg-GitHub-Connector/1.0',
            'X-GitHub-Api-Version': '2022-11-28'
        })

        return session

    def _make_api_request_with_retry(self, url: str, params: Dict = None, method: str = 'GET') -> Optional[requests.Response]:
        """Make API request with comprehensive error handling and retry logic."""
        for attempt in range(self.max_retries + 1):
            try:
                # Check rate limits before making request
                self._check_and_handle_rate_limits()

                # Make the request
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=30)
                elif method.upper() == 'POST':
                    response = self.session.post(url, json=params, timeout=30)
                else:
                    response = self.session.request(method, url, params=params, timeout=30)

                # Update rate limit info from headers
                self._update_rate_limit_info(response.headers)

                # Handle different response codes
                if response.status_code == 200:
                    return response
                elif response.status_code == 404:
                    logger.warning(f"Resource not found: {url}")
                    return None
                elif response.status_code == 403:
                    if 'rate limit' in response.text.lower():
                        logger.warning("Rate limit exceeded, waiting...")
                        self._handle_rate_limit_exceeded(response.headers)
                        continue
                    else:
                        logger.error(f"Access forbidden: {url}")
                        return None
                elif response.status_code == 401:
                    logger.error(f"Authentication failed: {url}")
                    return None
                elif response.status_code >= 500:
                    logger.warning(f"Server error {response.status_code}, retrying... (attempt {attempt + 1})")
                    if attempt < self.max_retries:
                        time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                        continue
                    else:
                        logger.error(f"Server error persisted after {self.max_retries} retries")
                        return None
                else:
                    logger.warning(f"Unexpected status code {response.status_code}: {url}")
                    return None

            except requests.exceptions.Timeout:
                logger.warning(f"Request timeout for {url}, retrying... (attempt {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    logger.error(f"Request timeout persisted after {self.max_retries} retries")
                    return None

            except requests.exceptions.ConnectionError:
                logger.warning(f"Connection error for {url}, retrying... (attempt {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    logger.error(f"Connection error persisted after {self.max_retries} retries")
                    return None

            except Exception as e:
                logger.error(f"Unexpected error making request to {url}: {str(e)}")
                return None

        return None

    def _check_and_handle_rate_limits(self):
        """Check rate limits and wait if necessary."""
        if self.rate_limit_remaining <= 10:
            if self.rate_limit_reset_time and datetime.now() < self.rate_limit_reset_time:
                wait_time = (self.rate_limit_reset_time - datetime.now()).total_seconds()
                logger.warning(f"Rate limit low ({self.rate_limit_remaining}), waiting {wait_time:.1f} seconds")
                time.sleep(wait_time + 1)

                # Refresh rate limit info
                try:
                    rate_limit_response = self.session.get("https://api.github.com/rate_limit", timeout=10)
                    if rate_limit_response.status_code == 200:
                        rate_data = rate_limit_response.json()
                        self.rate_limit_remaining = rate_data.get('resources', {}).get('core', {}).get('remaining', 5000)
                        reset_timestamp = rate_data.get('resources', {}).get('core', {}).get('reset', 0)
                        self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)
                except Exception as e:
                    logger.warning(f"Failed to refresh rate limit info: {str(e)}")

    def _update_rate_limit_info(self, headers: Dict[str, str]):
        """Update rate limit information from response headers."""
        try:
            if 'X-RateLimit-Remaining' in headers:
                self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in headers:
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)

            logger.debug(f"Rate limit - Remaining: {self.rate_limit_remaining}, Reset: {self.rate_limit_reset_time}")
        except (ValueError, KeyError) as e:
            logger.debug(f"Could not parse rate limit headers: {str(e)}")

    def _handle_rate_limit_exceeded(self, headers: Dict[str, str]):
        """Handle rate limit exceeded scenario."""
        try:
            if 'X-RateLimit-Reset' in headers:
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                reset_time = datetime.fromtimestamp(reset_timestamp)
                wait_time = (reset_time - datetime.now()).total_seconds()

                if wait_time > 0:
                    logger.warning(f"Rate limit exceeded, waiting {wait_time:.1f} seconds until reset")
                    time.sleep(wait_time + 5)  # Add 5 seconds buffer
                    self.rate_limit_remaining = 5000  # Reset to default
                    self.rate_limit_reset_time = reset_time
        except Exception as e:
            logger.warning(f"Error handling rate limit: {str(e)}, waiting 60 seconds")
            time.sleep(60)

    def _safe_api_call(self, url: str, params: Dict = None, method: str = 'GET', context: str = "API call") -> Optional[Dict]:
        """Safely make API call with comprehensive error handling."""
        try:
            response = self._make_api_request_with_retry(url, params, method)
            if response and response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed {context}: {url}")
                return None
        except Exception as e:
            logger.error(f"Error in {context}: {str(e)}")
            return None

    def test_authentication(self) -> Optional[Dict]:
        """Test GitHub authentication."""
        return self._safe_api_call("https://api.github.com/user", context="authentication test")

    # User API methods
    def get_authenticated_user(self) -> Optional[Dict]:
        """Get the authenticated user's profile."""
        return self._safe_api_call("https://api.github.com/user", context="get authenticated user")

    def get_user_organizations(self) -> List[Dict]:
        """Get organizations for the authenticated user."""
        result = self._safe_api_call("https://api.github.com/user/orgs", context="get user organizations")
        return result if result else []

    def get_user_repositories(self) -> List[Dict]:
        """Get repositories for the authenticated user."""
        repositories = []
        page = 1
        per_page = 100

        while True:
            params = {
                'type': 'all',
                'sort': 'updated',
                'per_page': per_page,
                'page': page
            }

            result = self._safe_api_call("https://api.github.com/user/repos", params=params, context="get user repositories")
            if not result:
                break

            repositories.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return repositories

    # Organization API methods
    def get_organization(self, org_login: str) -> Optional[Dict]:
        """Get organization details."""
        return self._safe_api_call(f"https://api.github.com/orgs/{org_login}", context=f"get organization {org_login}")

    def get_organization_members(self, org_login: str) -> List[Dict]:
        """Get all members of an organization."""
        members = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/orgs/{org_login}/members", params=params,
                                       context=f"get organization {org_login} members")
            if not result:
                break

            members.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return members

    def get_organization_teams(self, org_login: str) -> List[Dict]:
        """Get all teams of an organization."""
        teams = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/orgs/{org_login}/teams", params=params,
                                       context=f"get organization {org_login} teams")
            if not result:
                break

            teams.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return teams

    def get_organization_repositories(self, org_login: str) -> List[Dict]:
        """Get all repositories of an organization."""
        repositories = []
        page = 1
        per_page = 100

        while True:
            params = {'type': 'all', 'sort': 'updated', 'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/orgs/{org_login}/repos", params=params,
                                       context=f"get organization {org_login} repositories")
            if not result:
                break

            repositories.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return repositories

    def get_team_members(self, org_login: str, team_slug: str) -> List[Dict]:
        """Get members of a specific team."""
        members = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/members",
                                       params=params, context=f"get team {team_slug} members")
            if not result:
                break

            members.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return members

    def get_team_repositories(self, org_login: str, team_slug: str) -> List[Dict]:
        """Get repositories accessible by a specific team."""
        repositories = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/repos",
                                       params=params, context=f"get team {team_slug} repositories")
            if not result:
                break

            repositories.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return repositories

    # Repository API methods
    def get_repository(self, owner: str, repo: str) -> Optional[Dict]:
        """Get repository details."""
        return self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}",
                                 context=f"get repository {owner}/{repo}")

    def get_repository_issues(self, owner: str, repo: str, state: str = 'all', limit: int = 100) -> List[Dict]:
        """Get repository issues."""
        issues = []
        page = 1
        per_page = min(limit, 100)

        while len(issues) < limit:
            params = {'state': state, 'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/issues", params=params,
                                       context=f"get repository {owner}/{repo} issues")
            if not result:
                break

            issues.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return issues[:limit]

    def get_repository_pull_requests(self, owner: str, repo: str, state: str = 'all', limit: int = 100) -> List[Dict]:
        """Get repository pull requests."""
        pull_requests = []
        page = 1
        per_page = min(limit, 100)

        while len(pull_requests) < limit:
            params = {'state': state, 'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/pulls", params=params,
                                       context=f"get repository {owner}/{repo} pull requests")
            if not result:
                break

            pull_requests.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return pull_requests[:limit]

    def get_repository_commits(self, owner: str, repo: str, limit: int = 50) -> List[Dict]:
        """Get repository commits."""
        commits = []
        page = 1
        per_page = min(limit, 100)

        while len(commits) < limit:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/commits", params=params,
                                       context=f"get repository {owner}/{repo} commits")
            if not result:
                break

            commits.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return commits[:limit]

    def get_repository_branches(self, owner: str, repo: str) -> List[Dict]:
        """Get repository branches."""
        branches = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/branches", params=params,
                                       context=f"get repository {owner}/{repo} branches")
            if not result:
                break

            branches.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return branches

    def get_repository_tags(self, owner: str, repo: str) -> List[Dict]:
        """Get repository tags."""
        tags = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/tags", params=params,
                                       context=f"get repository {owner}/{repo} tags")
            if not result:
                break

            tags.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return tags

    def get_repository_releases(self, owner: str, repo: str) -> List[Dict]:
        """Get repository releases."""
        releases = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/releases", params=params,
                                       context=f"get repository {owner}/{repo} releases")
            if not result:
                break

            releases.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return releases

    def get_repository_contents(self, owner: str, repo: str, path: str = "") -> List[Dict]:
        """Get repository file structure."""
        result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/contents/{path}",
                                   context=f"get repository {owner}/{repo} contents")
        return result if isinstance(result, list) else [result] if result else []

    def get_file_content(self, owner: str, repo: str, path: str) -> Optional[str]:
        """Get file content from repository."""
        result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/contents/{path}",
                                   context=f"get file content {owner}/{repo}/{path}")
        if result and result.get('content'):
            try:
                return base64.b64decode(result['content']).decode('utf-8')
            except Exception as e:
                logger.warning(f"Failed to decode file content: {str(e)}")
                return None
        return None

    def get_issue_comments(self, owner: str, repo: str, issue_number: int) -> List[Dict]:
        """Get comments for a specific issue."""
        comments = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/issues/{issue_number}/comments",
                                       params=params, context=f"get issue {issue_number} comments")
            if not result:
                break

            comments.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return comments

    def get_pr_reviews(self, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Get reviews for a specific pull request."""
        reviews = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/reviews",
                                       params=params, context=f"get PR {pr_number} reviews")
            if not result:
                break

            reviews.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return reviews

    def get_pr_review_comments(self, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Get review comments for a specific pull request."""
        comments = []
        page = 1
        per_page = 100

        while True:
            params = {'per_page': per_page, 'page': page}
            result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/comments",
                                       params=params, context=f"get PR {pr_number} review comments")
            if not result:
                break

            comments.extend(result)

            if len(result) < per_page:
                break

            page += 1

        return comments


class GitHubEntityMapper:
    """
    Maps raw GitHub API responses to standardized schema entities.
    Handles data normalization, property mapping, and ensures all entities
    conform to the github_schema.yml definitions.
    """

    def __init__(self):
        pass

    def map_user(self, user_data: Dict, organisation_id: str) -> Dict:
        """Map GitHub user data to schema format."""
        return {
            'id': user_data.get('id'),
            'organisation_id': organisation_id,
            'login': user_data.get('login'),
            'name': user_data.get('name'),
            'email': user_data.get('email'),
            'html_url': user_data.get('html_url'),
            'type': user_data.get('type', 'User'),
            'created_at': user_data.get('created_at'),
            'updated_at': user_data.get('updated_at')
        }

    def map_organization(self, org_data: Dict, organisation_id: str) -> Dict:
        """Map GitHub organization data to schema format."""
        return {
            'id': org_data.get('id'),
            'organisation_id': organisation_id,
            'login': org_data.get('login'),
            'name': org_data.get('name'),
            'description': org_data.get('description'),
            'html_url': org_data.get('html_url'),
            'public_repos': org_data.get('public_repos', 0),
            'public_gists': org_data.get('public_gists', 0),
            'followers': org_data.get('followers', 0),
            'following': org_data.get('following', 0),
            'created_at': org_data.get('created_at'),
            'updated_at': org_data.get('updated_at')
        }

    def map_repository(self, repo_data: Dict, organisation_id: str) -> Dict:
        """Map GitHub repository data to schema format."""
        return {
            'id': repo_data.get('id'),
            'organisation_id': organisation_id,
            'name': repo_data.get('name'),
            'full_name': repo_data.get('full_name'),
            'description': repo_data.get('description'),
            'private': repo_data.get('private', False),
            'html_url': repo_data.get('html_url'),
            'default_branch': repo_data.get('default_branch'),
            'stargazers_count': repo_data.get('stargazers_count', 0),
            'watchers_count': repo_data.get('watchers_count', 0),
            'forks_count': repo_data.get('forks_count', 0),
            'open_issues_count': repo_data.get('open_issues_count', 0),
            'archived': repo_data.get('archived', False),
            'created_at': repo_data.get('created_at'),
            'updated_at': repo_data.get('updated_at')
        }

    def map_issue(self, issue_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub issue data to schema format."""
        return {
            'id': issue_data.get('id'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'number': issue_data.get('number'),
            'title': issue_data.get('title'),
            'body': issue_data.get('body'),
            'state': issue_data.get('state'),
            'html_url': issue_data.get('html_url'),
            'created_at': issue_data.get('created_at'),
            'updated_at': issue_data.get('updated_at'),
            'closed_at': issue_data.get('closed_at')
        }

    def map_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub pull request data to schema format."""
        return {
            'id': pr_data.get('id'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'number': pr_data.get('number'),
            'title': pr_data.get('title'),
            'body': pr_data.get('body'),
            'state': pr_data.get('state'),
            'html_url': pr_data.get('html_url'),
            'head_branch': pr_data.get('head', {}).get('ref'),
            'base_branch': pr_data.get('base', {}).get('ref'),
            'merged': pr_data.get('merged', False),
            'created_at': pr_data.get('created_at'),
            'updated_at': pr_data.get('updated_at'),
            'merged_at': pr_data.get('merged_at')
        }

    def map_commit(self, commit_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub commit data to schema format."""
        commit_info = commit_data.get('commit', {})
        author_info = commit_info.get('author', {})
        committer_info = commit_info.get('committer', {})

        return {
            'sha': commit_data.get('sha'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'message': commit_info.get('message'),
            'author_name': author_info.get('name'),
            'author_email': author_info.get('email'),
            'committer_name': committer_info.get('name'),
            'committer_email': committer_info.get('email'),
            'html_url': commit_data.get('html_url'),
            'date': author_info.get('date'),
            'created_at': author_info.get('date')
        }

    def map_branch(self, branch_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub branch data to schema format."""
        return {
            'name': branch_data.get('name'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'last_commit_sha': branch_data.get('commit', {}).get('sha'),
            'protected': branch_data.get('protected', False),
            'default': False,  # Will be set separately for default branch
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

    def map_tag(self, tag_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub tag data to schema format."""
        return {
            'name': tag_data.get('name'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'sha': tag_data.get('commit', {}).get('sha'),
            'commit_sha': tag_data.get('commit', {}).get('sha'),
            'message': tag_data.get('message', ''),
            'tagger_name': tag_data.get('tagger', {}).get('name', ''),
            'tagger_email': tag_data.get('tagger', {}).get('email', ''),
            'created_at': tag_data.get('tagger', {}).get('date', datetime.utcnow().isoformat())
        }

    def map_release(self, release_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub release data to schema format."""
        return {
            'id': release_data.get('id'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'tag_name': release_data.get('tag_name'),
            'name': release_data.get('name'),
            'body': release_data.get('body'),
            'draft': release_data.get('draft', False),
            'prerelease': release_data.get('prerelease', False),
            'html_url': release_data.get('html_url'),
            'created_at': release_data.get('created_at'),
            'published_at': release_data.get('published_at')
        }

    def map_team(self, team_data: Dict, organisation_id: str, organization_id: int) -> Dict:
        """Map GitHub team data to schema format."""
        return {
            'id': team_data.get('id'),
            'organisation_id': organisation_id,
            'organization_id': organization_id,
            'name': team_data.get('name'),
            'slug': team_data.get('slug'),
            'description': team_data.get('description'),
            'privacy': team_data.get('privacy'),
            'permission': team_data.get('permission'),
            'html_url': team_data.get('html_url'),
            'created_at': team_data.get('created_at'),
            'updated_at': team_data.get('updated_at')
        }

    def map_code_file(self, file_data: Dict, organisation_id: str, repository_id: int, content: str = None) -> Dict:
        """Map GitHub file data to schema format."""
        return {
            'path': file_data.get('path'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'sha': file_data.get('sha'),
            'name': file_data.get('name'),
            'content': content,
            'size': file_data.get('size', 0),
            'html_url': file_data.get('html_url'),
            'download_url': file_data.get('download_url'),
            'content_type': self._determine_content_type(file_data.get('path', '')),
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

    def _determine_content_type(self, file_path: str) -> str:
        """Determine content type based on file path."""
        if not file_path:
            return 'code'

        file_extension = file_path.split('.')[-1].lower() if '.' in file_path else ''
        file_name = file_path.split('/')[-1].lower()

        code_extensions = {
            'py', 'js', 'ts', 'java', 'cpp', 'c', 'h', 'hpp', 'cs', 'php', 'rb', 'go', 'rs',
            'swift', 'kt', 'scala', 'clj', 'hs', 'ml', 'r', 'matlab', 'm', 'pl', 'sh', 'bash',
            'zsh', 'fish', 'sql', 'html', 'css', 'scss', 'sass', 'less', 'vue', 'jsx', 'tsx'
        }

        documentation_extensions = {'md', 'rst', 'txt', 'adoc', 'asciidoc', 'org', 'wiki'}
        config_extensions = {'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'}
        data_extensions = {'csv', 'tsv', 'log'}

        if file_extension in code_extensions:
            return 'code'
        elif file_extension in documentation_extensions:
            return 'documentation'
        elif file_extension in config_extensions:
            return 'config'
        elif file_extension in data_extensions:
            return 'data'
        elif any(doc_word in file_name for doc_word in ['readme', 'license', 'changelog', 'contributing']):
            return 'documentation'
        else:
            return 'code'  # Default to code


class GitHubSyncService:
    """
    Orchestrates the complete GitHub data fetching and storage process.
    Provides clean separation of concerns with methods for organization-level
    and user-level synchronization following Google Drive patterns.
    """

    def __init__(self, api_client: GitHubAPIClient, entity_mapper: GitHubEntityMapper):
        self.api_client = api_client
        self.entity_mapper = entity_mapper

        # Initialize query instances (following Google Drive pattern)
        self.user_queries = GitHubUserQueries()
        self.organization_queries = GitHubOrganizationQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.team_queries = GitHubTeamQueries()
        self.tag_queries = GitHubTagQueries()
        self.release_queries = GitHubReleaseQueries()
        self.comment_queries = GitHubCommentQueries()
        self.review_queries = GitHubReviewQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.sync_queries = GitHubSyncQueries()

    def sync_organization(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync GitHub organization data following Google Drive patterns.

        Args:
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub organization sync for organisation: {organisation_id}")

            # Test authentication
            auth_test = self.api_client.test_authentication()
            if not auth_test:
                return False, "GitHub authentication failed", 0

            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            items_synced = 0

            # 1. Fetch and sync user organizations
            organizations = self.api_client.get_user_organizations()
            for org_data in organizations:
                # Create organization node
                org_entity = self.entity_mapper.map_organization(org_data, organisation_id)
                self._create_or_update_organization(org_entity)
                items_synced += 1

                # Sync organization details
                org_details = self.api_client.get_organization(org_data['login'])
                if org_details:
                    org_entity = self.entity_mapper.map_organization(org_details, organisation_id)
                    self._create_or_update_organization(org_entity)

                # 2. Sync organization members
                members = self.api_client.get_organization_members(org_data['login'])
                for member_data in members:
                    user_entity = self.entity_mapper.map_user(member_data, organisation_id)
                    self._create_or_update_user(user_entity)
                    self._create_organization_member_relationship(user_entity['id'], org_entity['id'])
                    items_synced += 1

                # 3. Sync organization teams
                teams = self.api_client.get_organization_teams(org_data['login'])
                for team_data in teams:
                    team_entity = self.entity_mapper.map_team(team_data, organisation_id, org_entity['id'])
                    self._create_or_update_team(team_entity)
                    items_synced += 1

                    # Sync team members
                    team_members = self.api_client.get_team_members(org_data['login'], team_data['slug'])
                    for member_data in team_members:
                        user_entity = self.entity_mapper.map_user(member_data, organisation_id)
                        self._create_or_update_user(user_entity)
                        self._create_team_member_relationship(user_entity['id'], team_entity['id'])
                        items_synced += 1

                # 4. Sync organization repositories
                repositories = self.api_client.get_organization_repositories(org_data['login'])
                for repo_data in repositories:
                    repo_items = self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)
                    items_synced += repo_items

            logger.info(f"Successfully completed GitHub organization sync: {items_synced} items")
            return True, f"Successfully synced GitHub organizations", items_synced

        except Exception as e:
            logger.error(f"Error in GitHub organization sync: {str(e)}")
            return False, f"Organization sync failed: {str(e)}", 0

    def sync_user(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync GitHub user data following Google Drive patterns.

        Args:
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub user sync for organisation: {organisation_id}")

            # Test authentication
            auth_test = self.api_client.test_authentication()
            if not auth_test:
                return False, "GitHub authentication failed", 0

            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            items_synced = 0

            # 1. Sync authenticated user profile
            user_data = self.api_client.get_authenticated_user()
            if user_data:
                user_entity = self.entity_mapper.map_user(user_data, organisation_id)
                self._create_or_update_user(user_entity)
                items_synced += 1

            # 2. Sync user repositories
            repositories = self.api_client.get_user_repositories()
            for repo_data in repositories:
                repo_items = self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)
                items_synced += repo_items

            # 3. Sync user organizations (if any)
            organizations = self.api_client.get_user_organizations()
            for org_data in organizations:
                org_entity = self.entity_mapper.map_organization(org_data, organisation_id)
                self._create_or_update_organization(org_entity)

                if user_data:
                    self._create_organization_member_relationship(user_entity['id'], org_entity['id'])
                items_synced += 1

            logger.info(f"Successfully completed GitHub user sync: {items_synced} items")
            return True, f"Successfully synced GitHub user data", items_synced

        except Exception as e:
            logger.error(f"Error in GitHub user sync: {str(e)}")
            return False, f"User sync failed: {str(e)}", 0

    def sync_repository(self, repo_data: Dict, organisation_id: str, full_sync: bool = False) -> int:
        """
        Sync a single repository with all its data.

        Args:
            repo_data: Repository data from GitHub API
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Number of items synced
        """
        return self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)

    def _sync_repository_comprehensive(self, repo_data: Dict, organisation_id: str, full_sync: bool = False) -> int:
        """Comprehensive repository sync following Google Drive patterns."""
        try:
            items_synced = 0
            owner = repo_data.get('owner', {}).get('login')
            repo_name = repo_data.get('name')

            if not owner or not repo_name:
                logger.warning(f"Invalid repository data: missing owner or name")
                return 0

            logger.info(f"Syncing repository: {owner}/{repo_name}")

            # 1. Create repository node
            repo_entity = self.entity_mapper.map_repository(repo_data, organisation_id)
            self._create_or_update_repository(repo_entity)
            items_synced += 1

            # 2. Sync repository owner
            if repo_data.get('owner'):
                owner_entity = self.entity_mapper.map_user(repo_data['owner'], organisation_id)
                self._create_or_update_user(owner_entity)
                self._create_repository_ownership_relationship(owner_entity['id'], repo_entity['id'])
                items_synced += 1

            # 3. Sync branches
            branches = self.api_client.get_repository_branches(owner, repo_name)
            for branch_data in branches:
                branch_entity = self.entity_mapper.map_branch(branch_data, organisation_id, repo_entity['id'])
                if branch_data['name'] == repo_data.get('default_branch'):
                    branch_entity['default'] = True
                self._create_or_update_branch(branch_entity)
                items_synced += 1

            # 4. Sync tags
            tags = self.api_client.get_repository_tags(owner, repo_name)
            for tag_data in tags:
                tag_entity = self.entity_mapper.map_tag(tag_data, organisation_id, repo_entity['id'])
                self._create_or_update_tag(tag_entity)
                items_synced += 1

            # 5. Sync releases
            releases = self.api_client.get_repository_releases(owner, repo_name)
            for release_data in releases:
                release_entity = self.entity_mapper.map_release(release_data, organisation_id, repo_entity['id'])
                self._create_or_update_release(release_entity)
                items_synced += 1

            if full_sync:
                # 6. Sync issues
                issues = self.api_client.get_repository_issues(owner, repo_name, limit=200)
                for issue_data in issues:
                    # Skip pull requests (they appear in issues API)
                    if issue_data.get('pull_request'):
                        continue

                    issue_entity = self.entity_mapper.map_issue(issue_data, organisation_id, repo_entity['id'])
                    self._create_or_update_issue(issue_entity)
                    items_synced += 1

                    # Sync issue comments
                    comments = self.api_client.get_issue_comments(owner, repo_name, issue_data['number'])
                    for comment_data in comments:
                        self._create_or_update_comment(comment_data, organisation_id, issue_id=issue_entity['id'])
                        items_synced += 1

                # 7. Sync pull requests
                pull_requests = self.api_client.get_repository_pull_requests(owner, repo_name, limit=200)
                for pr_data in pull_requests:
                    pr_entity = self.entity_mapper.map_pull_request(pr_data, organisation_id, repo_entity['id'])
                    self._create_or_update_pull_request(pr_entity)
                    items_synced += 1

                    # Sync PR reviews
                    reviews = self.api_client.get_pr_reviews(owner, repo_name, pr_data['number'])
                    for review_data in reviews:
                        self._create_or_update_review(review_data, organisation_id, pr_entity['id'])
                        items_synced += 1

                    # Sync PR review comments
                    review_comments = self.api_client.get_pr_review_comments(owner, repo_name, pr_data['number'])
                    for comment_data in review_comments:
                        self._create_or_update_review_comment(comment_data, organisation_id, pr_entity['id'])
                        items_synced += 1

                # 8. Sync commits
                commits = self.api_client.get_repository_commits(owner, repo_name, limit=100)
                for commit_data in commits:
                    commit_entity = self.entity_mapper.map_commit(commit_data, organisation_id, repo_entity['id'])
                    self._create_or_update_commit(commit_entity)
                    items_synced += 1

                # 9. Sync file structure (limited to avoid rate limits)
                try:
                    contents = self.api_client.get_repository_contents(owner, repo_name)
                    for content_item in contents[:50]:  # Limit to first 50 items
                        if content_item.get('type') == 'file':
                            file_content = self.api_client.get_file_content(owner, repo_name, content_item['path'])
                            file_entity = self.entity_mapper.map_code_file(content_item, organisation_id, repo_entity['id'], file_content)
                            self._create_or_update_code_file(file_entity)
                            items_synced += 1
                except Exception as e:
                    logger.warning(f"Failed to sync file structure for {owner}/{repo_name}: {str(e)}")

            # 10. Create organizational relationships
            self._create_organizational_relationships(repo_entity['id'], organisation_id)

            logger.info(f"Successfully synced repository {owner}/{repo_name}: {items_synced} items")
            return items_synced

        except Exception as e:
            logger.error(f"Error syncing repository {owner}/{repo_name}: {str(e)}")
            return 0

    # Helper methods for creating and updating entities
    def _create_or_update_user(self, user_entity: Dict):
        """Create or update GitHub user following Google Drive patterns."""
        try:
            execute_write_query(self.user_queries.CREATE_OR_UPDATE_USER, user_entity)
            logger.debug(f"Created/updated GitHub user: {user_entity.get('login')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub user: {str(e)}")

    def _create_or_update_organization(self, org_entity: Dict):
        """Create or update GitHub organization following Google Drive patterns."""
        try:
            execute_write_query(self.organization_queries.CREATE_OR_UPDATE_ORGANIZATION, org_entity)
            logger.debug(f"Created/updated GitHub organization: {org_entity.get('login')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub organization: {str(e)}")

    def _create_or_update_repository(self, repo_entity: Dict):
        """Create or update GitHub repository following Google Drive patterns."""
        try:
            execute_write_query(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, repo_entity)
            logger.debug(f"Created/updated GitHub repository: {repo_entity.get('full_name')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub repository: {str(e)}")

    def _create_or_update_issue(self, issue_entity: Dict):
        """Create or update GitHub issue following Google Drive patterns."""
        try:
            execute_write_query(self.issue_queries.CREATE_OR_UPDATE_ISSUE, issue_entity)
            logger.debug(f"Created/updated GitHub issue: {issue_entity.get('title')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub issue: {str(e)}")

    def _create_or_update_pull_request(self, pr_entity: Dict):
        """Create or update GitHub pull request following Google Drive patterns."""
        try:
            execute_write_query(self.pull_request_queries.CREATE_OR_UPDATE_PULL_REQUEST, pr_entity)
            logger.debug(f"Created/updated GitHub pull request: {pr_entity.get('title')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub pull request: {str(e)}")

    def _create_or_update_commit(self, commit_entity: Dict):
        """Create or update GitHub commit following Google Drive patterns."""
        try:
            execute_write_query(self.commit_queries.CREATE_OR_UPDATE_COMMIT, commit_entity)
            logger.debug(f"Created/updated GitHub commit: {commit_entity.get('sha')[:8]}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub commit: {str(e)}")

    def _create_or_update_branch(self, branch_entity: Dict):
        """Create or update GitHub branch following Google Drive patterns."""
        try:
            execute_write_query(self.repository_queries.CREATE_OR_UPDATE_BRANCH, branch_entity)
            logger.debug(f"Created/updated GitHub branch: {branch_entity.get('name')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub branch: {str(e)}")

    def _create_or_update_tag(self, tag_entity: Dict):
        """Create or update GitHub tag following Google Drive patterns."""
        try:
            execute_write_query(self.tag_queries.CREATE_OR_UPDATE_TAG, tag_entity)
            logger.debug(f"Created/updated GitHub tag: {tag_entity.get('name')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub tag: {str(e)}")

    def _create_or_update_release(self, release_entity: Dict):
        """Create or update GitHub release following Google Drive patterns."""
        try:
            execute_write_query(self.release_queries.CREATE_OR_UPDATE_RELEASE, release_entity)
            logger.debug(f"Created/updated GitHub release: {release_entity.get('name')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub release: {str(e)}")

    def _create_or_update_team(self, team_entity: Dict):
        """Create or update GitHub team following Google Drive patterns."""
        try:
            execute_write_query(self.team_queries.CREATE_OR_UPDATE_TEAM, team_entity)
            logger.debug(f"Created/updated GitHub team: {team_entity.get('name')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub team: {str(e)}")

    def _create_or_update_code_file(self, file_entity: Dict):
        """Create or update GitHub code file following Google Drive patterns."""
        try:
            execute_write_query(self.file_queries.CREATE_OR_UPDATE_FILE, file_entity)
            logger.debug(f"Created/updated GitHub file: {file_entity.get('path')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub file: {str(e)}")

    def _create_or_update_comment(self, comment_data: Dict, organisation_id: str, issue_id: int = None, pull_request_id: int = None):
        """Create or update GitHub comment following Google Drive patterns."""
        try:
            params = {
                'id': comment_data.get('id'),
                'organisation_id': organisation_id,
                'body': comment_data.get('body'),
                'html_url': comment_data.get('html_url'),
                'created_at': comment_data.get('created_at'),
                'updated_at': comment_data.get('updated_at')
            }

            if issue_id:
                params['issue_id'] = issue_id
            if pull_request_id:
                params['pull_request_id'] = pull_request_id

            execute_write_query(self.comment_queries.CREATE_OR_UPDATE_COMMENT, params)
            logger.debug(f"Created/updated GitHub comment: {comment_data.get('id')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub comment: {str(e)}")

    def _create_or_update_review(self, review_data: Dict, organisation_id: str, pull_request_id: int):
        """Create or update GitHub review following Google Drive patterns."""
        try:
            params = {
                'id': review_data.get('id'),
                'organisation_id': organisation_id,
                'pull_request_id': pull_request_id,
                'state': review_data.get('state'),
                'body': review_data.get('body'),
                'html_url': review_data.get('html_url'),
                'submitted_at': review_data.get('submitted_at')
            }

            execute_write_query(self.review_queries.CREATE_OR_UPDATE_REVIEW, params)
            logger.debug(f"Created/updated GitHub review: {review_data.get('id')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub review: {str(e)}")

    def _create_or_update_review_comment(self, comment_data: Dict, organisation_id: str, pull_request_id: int):
        """Create or update GitHub review comment following Google Drive patterns."""
        try:
            params = {
                'id': comment_data.get('id'),
                'organisation_id': organisation_id,
                'pull_request_id': pull_request_id,
                'path': comment_data.get('path'),
                'position': comment_data.get('position'),
                'line': comment_data.get('line'),
                'body': comment_data.get('body'),
                'html_url': comment_data.get('html_url'),
                'created_at': comment_data.get('created_at'),
                'updated_at': comment_data.get('updated_at')
            }

            execute_write_query(self.comment_queries.CREATE_OR_UPDATE_REVIEW_COMMENT, params)
            logger.debug(f"Created/updated GitHub review comment: {comment_data.get('id')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub review comment: {str(e)}")

    # Relationship creation methods
    def _create_organization_member_relationship(self, user_id: int, organization_id: int):
        """Create organization member relationship following Google Drive patterns."""
        try:
            params = {
                'user_id': user_id,
                'organization_id': organization_id,
                'joined_at': datetime.utcnow().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANIZATION_MEMBER_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating organization member relationship: {str(e)}")

    def _create_team_member_relationship(self, user_id: int, team_id: int):
        """Create team member relationship following Google Drive patterns."""
        try:
            params = {
                'user_id': user_id,
                'team_id': team_id,
                'joined_at': datetime.utcnow().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_TEAM_MEMBER_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating team member relationship: {str(e)}")

    def _create_repository_ownership_relationship(self, user_id: int, repository_id: int):
        """Create repository ownership relationship following Google Drive patterns."""
        try:
            params = {
                'user_id': user_id,
                'repository_id': repository_id,
                'ownership_type': 'owner',
                'granted_at': datetime.utcnow().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_REPOSITORY_OWNERSHIP_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating repository ownership relationship: {str(e)}")

    def _create_organizational_relationships(self, repository_id: int, organisation_id: str):
        """Create organizational relationships following Google Drive patterns."""
        try:
            # Create organization-repository relationships (mirroring GDrive's organization access)
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'granted_at': datetime.utcnow().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP, params)

            # Create department-repository relationships for all departments (mirroring GDrive's department access)
            execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP, params)

            logger.debug(f"Created organizational relationships for repository: {repository_id}")
        except Exception as e:
            logger.error(f"Error creating organizational relationships: {str(e)}")

class GitHubService:
    """
    Refactored GitHub service using modular components.
    Provides clean integration with the existing connector framework
    while using the new GitHubAPIClient, GitHubEntityMapper, and GitHubSyncService.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.pinecone_service = PineconeService()

        # Initialize modular components
        self.api_client = None  # Will be initialized when credentials are available
        self.entity_mapper = GitHubEntityMapper()
        self.sync_service = None  # Will be initialized when API client is ready

        # Initialize query instances for backward compatibility
        self.user_queries = GitHubUserQueries()
        self.organization_queries = GitHubOrganizationQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.team_queries = GitHubTeamQueries()
        self.tag_queries = GitHubTagQueries()
        self.release_queries = GitHubReleaseQueries()
        self.comment_queries = GitHubCommentQueries()
        self.review_queries = GitHubReviewQueries()
        self.workflow_queries = GitHubWorkflowQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.sync_queries = GitHubSyncQueries()

    def _initialize_api_client(self, organisation_id: str) -> bool:
        """Initialize API client with credentials."""
        try:
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                logger.error(f"No GitHub credentials found for organisation {organisation_id}")
                return False

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                logger.error("GitHub token not found in credentials")
                return False

            self.api_client = GitHubAPIClient(token)
            self.sync_service = GitHubSyncService(self.api_client, self.entity_mapper)
            return True

        except Exception as e:
            logger.error(f"Error initializing GitHub API client: {str(e)}")
            return False
        
    # Backward compatibility methods - delegate to API client
    def _create_robust_session(self, token: str) -> requests.Session:
        """Create a robust session - delegates to API client for backward compatibility."""
        if not self.api_client or self.api_client.token != token:
            self.api_client = GitHubAPIClient(token)
        return self.api_client.session

    def _safe_api_call(self, session: requests.Session, url: str, params: Dict = None,
                      method: str = 'GET', context: str = "API call") -> Optional[Dict]:
        """Safely make API call - delegates to API client for backward compatibility."""
        if self.api_client:
            return self.api_client._safe_api_call(url, params, method, context)
        return None

    def extract_repo_info_from_url(self, github_url: str) -> Optional[Dict[str, str]]:
        """
        Extract repository owner and name from a GitHub URL.
        
        Args:
            github_url: The GitHub URL
            
        Returns:
            Dictionary with owner and repo name or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://github.com/owner/repo
            # Format 2: https://github.com/owner/repo/issues/123
            # Format 3: https://github.com/owner/repo/pull/456
            
            if "github.com/" in github_url:
                # Extract owner/repo from URL
                parts = github_url.split("github.com/")[1].split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                    return {"owner": owner, "repo": repo}
            
            logger.error(f"Unsupported GitHub URL format: {github_url}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting repo info from URL: {str(e)}")
            return None

    def sync_repository_by_url(self,
                              github_url: str,
                              agent_id: str,
                              user_id: Optional[str],
                              organisation_id: str,
                              full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync a specific GitHub repository by URL.
        
        Args:
            github_url: GitHub repository URL
            agent_id: Agent ID performing the sync
            user_id: User ID (optional)
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync
            
        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            # Validate required parameters
            if not github_url or not github_url.strip():
                logger.error("github_url is required for repository sync")
                return False, "github_url is required", 0

            if not organisation_id:
                logger.error("organisation_id is required for repository sync")
                return False, "organisation_id is required", 0

            if not agent_id:
                logger.error("agent_id is required for repository sync")
                return False, "agent_id is required", 0

            logger.info(f"Starting GitHub repository sync for URL: {github_url}")

            # Extract repository info from URL
            repo_info = self.extract_repo_info_from_url(github_url)
            if not repo_info:
                return False, "Failed to extract repository information from URL", 0

            owner = repo_info["owner"]
            repo_name = repo_info["repo"]

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0

            # Get repository data using the new API client
            repo_data = self.api_client.get_repository(owner, repo_name)
            if not repo_data:
                return False, f"Failed to fetch repository data for {owner}/{repo_name}", 0

            # Use the new sync service to sync the repository
            items_synced = self.sync_service.sync_repository(repo_data, organisation_id, full_sync)

            if items_synced > 0:
                logger.info(f"Successfully synced GitHub repository: {owner}/{repo_name}, items: {items_synced}")
                return True, f"Successfully synced repository {owner}/{repo_name}", items_synced
            else:
                return False, f"Failed to sync repository {owner}/{repo_name}", 0
                
        except Exception as e:
            logger.error(f"Error syncing GitHub repository: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0

    def _fetch_repository_data(self, session: requests.Session, owner: str, repo: str) -> Optional[Dict]:
        """Fetch repository data from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}"
            response = session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository data: {str(e)}")
            return None

    def _fetch_repository_issues(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository issues from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/issues"
            params = {'state': 'all', 'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository issues: {str(e)}")
            return []

    def _fetch_repository_pull_requests(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository pull requests from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
            params = {'state': 'all', 'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository pull requests: {str(e)}")
            return []

    def _fetch_repository_commits(self, session: requests.Session, owner: str, repo: str, limit: int = 50) -> List[Dict]:
        """Fetch repository commits from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository commits: {str(e)}")
            return []

    def _create_or_update_repository(self, repo_data: Dict, organisation_id: str):
        """Create or update repository in Neo4j following Google Drive patterns."""
        try:
            # Prepare parameters for repository creation
            params = {
                'repository_id': repo_data['id'],
                'organisation_id': organisation_id,
                'name': repo_data.get('name', ''),
                'full_name': repo_data.get('full_name', ''),
                'html_url': repo_data.get('html_url', ''),
                'description': repo_data.get('description', ''),
                'private': repo_data.get('private', False),
                'default_branch': repo_data.get('default_branch', 'main'),
                'stargazers_count': repo_data.get('stargazers_count', 0),
                'watchers_count': repo_data.get('watchers_count', 0),
                'forks_count': repo_data.get('forks_count', 0),
                'open_issues_count': repo_data.get('open_issues_count', 0),
                'language': repo_data.get('language', ''),
                'created_at': repo_data.get('created_at', datetime.now().isoformat()),
                'updated_at': repo_data.get('updated_at', datetime.now().isoformat()),
                'pushed_at': repo_data.get('pushed_at')
            }

            # Create or update the repository
            execute_write_query(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, params)

            # Create owner relationship if owner data exists
            if repo_data.get('owner'):
                # Create or update the user
                self._create_or_update_user(repo_data['owner'], organisation_id)

                # Create repository with owner relationship
                owner_params = {
                    'repository_id': repo_data['id'],
                    'organisation_id': organisation_id,
                    'owner_login': repo_data['owner'].get('login'),
                    'ownership_type': 'owner',
                    'granted_at': datetime.now().isoformat()
                }
                execute_write_query(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY_WITH_OWNER, owner_params)

                # If the owner is an organization, create GitHub organization node
                if repo_data['owner'].get('type') == 'Organization':
                    self._create_or_update_github_organization(repo_data['owner'], organisation_id)

                    # Link repository to GitHub organization
                    org_repo_params = {
                        'repository_id': repo_data['id'],
                        'github_org_id': repo_data['owner']['id'],
                        'organisation_id': organisation_id
                    }
                    execute_write_query(self.organization_queries.ADD_REPOSITORY_TO_ORGANIZATION, org_repo_params)

            # Create organizational relationships
            self._create_organizational_relationships(repo_data['id'], organisation_id)

        except Exception as e:
            logger.error(f"Error creating/updating repository: {str(e)}")

    def _create_or_update_issue(self, issue_data: Dict, organisation_id: str, repository_id: int):
        """Create or update issue in Neo4j following Google Drive patterns."""
        try:
            params = {
                'issue_id': issue_data['id'],
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'number': issue_data.get('number'),
                'title': issue_data.get('title', ''),
                'body': issue_data.get('body', ''),
                'state': issue_data.get('state', 'open'),
                'html_url': issue_data.get('html_url', ''),
                'created_at': issue_data.get('created_at', datetime.now().isoformat()),
                'updated_at': issue_data.get('updated_at', datetime.now().isoformat()),
                'closed_at': issue_data.get('closed_at'),
                'labels': [label.get('name', '') for label in issue_data.get('labels', [])]
            }

            # Create or update the issue
            execute_write_query(self.issue_queries.CREATE_OR_UPDATE_ISSUE, params)

            # Create creator relationship if user exists
            if issue_data.get('user'):
                self._create_or_update_user(issue_data['user'], organisation_id)
                creator_params = {
                    'issue_id': issue_data['id'],
                    'creator_login': issue_data['user'].get('login'),
                    'created_at': issue_data.get('created_at', datetime.now().isoformat())
                }
                execute_write_query(self.issue_queries.CREATE_ISSUE_CREATOR_RELATIONSHIP, creator_params)

            # Create assignee relationships if assignees exist
            for assignee in issue_data.get('assignees', []):
                self._create_or_update_user(assignee, organisation_id)
                assignee_params = {
                    'issue_id': issue_data['id'],
                    'assignee_login': assignee.get('login'),
                    'assigned_at': datetime.now().isoformat()
                }
                execute_write_query(self.issue_queries.CREATE_ISSUE_ASSIGNEE_RELATIONSHIP, assignee_params)

        except Exception as e:
            logger.error(f"Error creating/updating issue: {str(e)}")

    def _create_or_update_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int):
        """Create or update pull request in Neo4j following Google Drive patterns."""
        try:
            params = {
                'pr_id': pr_data['id'],
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'number': pr_data.get('number'),
                'title': pr_data.get('title', ''),
                'body': pr_data.get('body', ''),
                'state': pr_data.get('state', 'open'),
                'html_url': pr_data.get('html_url', ''),
                'created_at': pr_data.get('created_at', datetime.now().isoformat()),
                'updated_at': pr_data.get('updated_at', datetime.now().isoformat()),
                'closed_at': pr_data.get('closed_at'),
                'merged_at': pr_data.get('merged_at'),
                'head_ref': pr_data.get('head', {}).get('ref', ''),
                'base_ref': pr_data.get('base', {}).get('ref', ''),
                'draft': pr_data.get('draft', False)
            }

            # Create or update the pull request
            execute_write_query(self.pull_request_queries.CREATE_OR_UPDATE_PULL_REQUEST, params)

            # Create creator relationship if user exists
            if pr_data.get('user'):
                self._create_or_update_user(pr_data['user'], organisation_id)
                creator_params = {
                    'pr_id': pr_data['id'],
                    'creator_login': pr_data['user'].get('login'),
                    'created_at': pr_data.get('created_at', datetime.now().isoformat())
                }
                execute_write_query(self.pull_request_queries.CREATE_PR_CREATOR_RELATIONSHIP, creator_params)

        except Exception as e:
            logger.error(f"Error creating/updating pull request: {str(e)}")

    def _create_or_update_commit(self, commit_data: Dict, organisation_id: str, repository_id: int):
        """Create or update commit in Neo4j following Google Drive patterns."""
        try:
            params = {
                'sha': commit_data['sha'],
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'message': commit_data.get('commit', {}).get('message', ''),
                'html_url': commit_data.get('html_url', ''),
                'author_name': commit_data.get('commit', {}).get('author', {}).get('name', ''),
                'author_email': commit_data.get('commit', {}).get('author', {}).get('email', ''),
                'committer_name': commit_data.get('commit', {}).get('committer', {}).get('name', ''),
                'committer_email': commit_data.get('commit', {}).get('committer', {}).get('email', ''),
                'created_at': commit_data.get('commit', {}).get('author', {}).get('date', datetime.now().isoformat()),
                'additions': commit_data.get('stats', {}).get('additions', 0),
                'deletions': commit_data.get('stats', {}).get('deletions', 0)
            }

            # Create or update the commit
            execute_write_query(self.commit_queries.CREATE_OR_UPDATE_COMMIT, params)

            # Create author relationship if author exists
            if commit_data.get('author'):
                self._create_or_update_user(commit_data['author'], organisation_id)
                author_params = {
                    'sha': commit_data['sha'],
                    'author_login': commit_data['author'].get('login'),
                    'authored_at': commit_data.get('commit', {}).get('author', {}).get('date', datetime.now().isoformat())
                }
                execute_write_query(self.commit_queries.CREATE_COMMIT_AUTHOR_RELATIONSHIP, author_params)

        except Exception as e:
            logger.error(f"Error creating/updating commit: {str(e)}")

    def _create_or_update_github_organization(self, org_data: Dict, organisation_id: str):
        """Create or update GitHub organization node following Google Drive patterns."""
        try:
            params = {
                'github_org_id': org_data['id'],
                'organisation_id': organisation_id,
                'login': org_data.get('login', ''),
                'name': org_data.get('name', ''),
                'description': org_data.get('description', ''),
                'html_url': org_data.get('html_url', ''),
                'created_at': org_data.get('created_at', datetime.now().isoformat()),
                'updated_at': org_data.get('updated_at', datetime.now().isoformat())
            }

            execute_write_query(self.organization_queries.CREATE_OR_UPDATE_GITHUB_ORGANIZATION, params)
            logger.info(f"Created/updated GitHub organization: {org_data.get('login')}")

        except Exception as e:
            logger.error(f"Error creating/updating GitHub organization: {str(e)}")

    def _create_or_update_user(self, user_data: Dict, organisation_id: str):
        """Create or update GitHub user in Neo4j following GDrive's user creation patterns."""
        try:
            params = {
                'id': user_data['id'],
                'organisation_id': organisation_id,
                'node_id': user_data.get('node_id'),
                'login': user_data.get('login'),
                'name': user_data.get('name'),
                'email': user_data.get('email'),
                'html_url': user_data.get('html_url'),
                'type': user_data.get('type'),
                'site_admin': user_data.get('site_admin', False),
                'created_at': user_data.get('created_at'),
                'updated_at': user_data.get('updated_at'),
                'creation_type': 'auto_created'  # Following GDrive's user creation pattern
            }

            # Use the new schema-based user creation (following Google Drive pattern)
            execute_write_query(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, params)

            # Create organizational relationships for the user (mirroring Google Drive pattern)
            self._create_github_user_organizational_relationships(user_data, organisation_id)

        except Exception as e:
            logger.error(f"Error creating/updating GitHub user: {str(e)}")

    def _create_github_user_organizational_relationships(self, user_data: Dict, organisation_id: str):
        """
        Create organizational relationships for GitHub users following Google Drive pattern.
        This mirrors how Google Drive creates User → Department → Organization relationships.
        """
        try:
            # If user has email, try to map to existing organizational User
            if user_data.get('email'):
                # Create mapping between organizational User and GitHubUser (mirroring Google Drive pattern)
                mapping_params = {
                    'email': user_data['email'],
                    'github_login': user_data.get('login'),
                    'organisation_id': organisation_id,
                    'mapped_at': datetime.now().isoformat()
                }
                execute_write_query(self.relationship_queries.CREATE_USER_GITHUB_USER_MAPPING, mapping_params)

                # Create department access for GitHub users through organizational users
                execute_write_query(self.relationship_queries.CREATE_GITHUB_USER_DEPARTMENT_ACCESS, {
                    'organisation_id': organisation_id
                })

        except Exception as e:
            logger.error(f"Error creating GitHub user organizational relationships: {str(e)}")

    def _create_or_update_issue(self, issue_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub issue in Neo4j."""
        try:
            params = {
                'id': issue_data['id'],
                'organisation_id': organisation_id,
                'node_id': issue_data.get('node_id'),
                'number': issue_data.get('number'),
                'html_url': issue_data.get('html_url'),
                'title': issue_data.get('title'),
                'body': issue_data.get('body'),
                'state': issue_data.get('state'),
                'labels': json.dumps([label.get('name') for label in issue_data.get('labels', [])]),
                'comments': issue_data.get('comments', 0),
                'created_at': issue_data.get('created_at'),
                'updated_at': issue_data.get('updated_at'),
                'closed_at': issue_data.get('closed_at')
            }
            
            execute_write_query(self.issue_queries.CREATE_ISSUE, params)
            
            # Create relationships
            if issue_data.get('user'):
                self._create_or_update_user(issue_data['user'], organisation_id)
                self._create_issue_creator_relationship(
                    issue_data['user']['id'],
                    issue_data['id'],
                    organisation_id
                )
            
            self._create_issue_repository_relationship(issue_data['id'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub issue: {str(e)}")

    def _create_or_update_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub pull request in Neo4j."""
        try:
            params = {
                'id': pr_data['id'],
                'organisation_id': organisation_id,
                'node_id': pr_data.get('node_id'),
                'number': pr_data.get('number'),
                'html_url': pr_data.get('html_url'),
                'title': pr_data.get('title'),
                'body': pr_data.get('body'),
                'state': pr_data.get('state'),
                'draft': pr_data.get('draft', False),
                'merged': pr_data.get('merged', False),
                'mergeable': pr_data.get('mergeable'),
                'additions': pr_data.get('additions', 0),
                'deletions': pr_data.get('deletions', 0),
                'changed_files': pr_data.get('changed_files', 0),
                'created_at': pr_data.get('created_at'),
                'updated_at': pr_data.get('updated_at'),
                'closed_at': pr_data.get('closed_at'),
                'merged_at': pr_data.get('merged_at')
            }
            
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST, params)
            
            # Create relationships
            if pr_data.get('user'):
                self._create_or_update_user(pr_data['user'], organisation_id)
                self._create_pull_request_creator_relationship(
                    pr_data['user']['id'],
                    pr_data['id'],
                    organisation_id
                )
            
            self._create_pull_request_repository_relationship(pr_data['id'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub pull request: {str(e)}")

    def _create_or_update_commit(self, commit_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub commit in Neo4j."""
        try:
            params = {
                'sha': commit_data['sha'],
                'organisation_id': organisation_id,
                'node_id': commit_data.get('node_id'),
                'html_url': commit_data.get('html_url'),
                'message': commit_data.get('commit', {}).get('message'),
                'additions': commit_data.get('stats', {}).get('additions', 0),
                'deletions': commit_data.get('stats', {}).get('deletions', 0),
                'total': commit_data.get('stats', {}).get('total', 0),
                'created_at': commit_data.get('commit', {}).get('author', {}).get('date')
            }
            
            execute_write_query(self.commit_queries.CREATE_COMMIT, params)
            
            # Create relationships
            if commit_data.get('author'):
                self._create_or_update_user(commit_data['author'], organisation_id)
                self._create_commit_author_relationship(
                    commit_data['author']['id'],
                    commit_data['sha'],
                    organisation_id
                )
            
            self._create_commit_repository_relationship(commit_data['sha'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub commit: {str(e)}")

    def _create_repository_owner_relationship(self, user_id: int, repository_id: int, organisation_id: str):
        """Create repository owner relationship."""
        try:
            params = {
                'user_id': user_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.repository_queries.CREATE_REPOSITORY_OWNER_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating repository owner relationship: {str(e)}")

    def _create_issue_creator_relationship(self, user_id: int, issue_id: int, organisation_id: str):
        """Create issue creator relationship."""
        try:
            params = {
                'user_id': user_id,
                'issue_id': issue_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.issue_queries.CREATE_ISSUE_CREATOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating issue creator relationship: {str(e)}")

    def _create_issue_repository_relationship(self, issue_id: int, repository_id: int, organisation_id: str):
        """Create issue repository relationship."""
        try:
            params = {
                'issue_id': issue_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.issue_queries.CREATE_ISSUE_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating issue repository relationship: {str(e)}")

    def _create_pull_request_creator_relationship(self, user_id: int, pull_request_id: int, organisation_id: str):
        """Create pull request creator relationship."""
        try:
            params = {
                'user_id': user_id,
                'pull_request_id': pull_request_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST_CREATOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating pull request creator relationship: {str(e)}")

    def _create_pull_request_repository_relationship(self, pull_request_id: int, repository_id: int, organisation_id: str):
        """Create pull request repository relationship."""
        try:
            params = {
                'pull_request_id': pull_request_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating pull request repository relationship: {str(e)}")

    def _create_commit_author_relationship(self, user_id: int, commit_sha: str, organisation_id: str):
        """Create commit author relationship."""
        try:
            params = {
                'user_id': user_id,
                'commit_sha': commit_sha,
                'organisation_id': organisation_id,
                'authored_at': datetime.now().isoformat()
            }
            execute_write_query(self.commit_queries.CREATE_COMMIT_AUTHOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating commit author relationship: {str(e)}")

    def _create_commit_repository_relationship(self, commit_sha: str, repository_id: int, organisation_id: str):
        """Create commit repository relationship."""
        try:
            params = {
                'commit_sha': commit_sha,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.commit_queries.CREATE_COMMIT_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating commit repository relationship: {str(e)}")

    def _create_organizational_relationships(self, repository_id: int, organisation_id: str):
        """Create relationships between GitHub repository and organizational entities following GDrive patterns."""
        try:
            # Create organisation-repository relationship (mirroring GDrive's Organization → GoogleDriveFolder pattern)
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'granted_at': datetime.now().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP, params)

            # Create department-repository relationships for all departments (mirroring GDrive's department access)
            execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP, params)

            # Create user-repository relationships through departments (mirroring GDrive's user access pattern)
            execute_write_query(self.relationship_queries.MAP_USER_REPOSITORY_ACCESS, params)

            # Ensure GENERAL department has access (exact GDrive pattern)
            execute_write_query(self.relationship_queries.MAP_GENERAL_DEPARTMENT_ACCESS, params)

        except Exception as e:
            logger.error(f"Error creating organizational relationships: {str(e)}")

    def create_github_source_node(self, organisation_id: str, source_name: str = "GitHub") -> tuple[bool, str]:
        """
        Create GitHub Source node and establish Organization → Source relationship.
        Uses the standard SourceQueries pattern like Google Drive and Jira.

        Args:
            organisation_id: The organization ID
            source_name: Name for the GitHub source

        Returns:
            Tuple containing success status and message
        """
        try:
            # Initialize source queries
            source_queries = SourceQueries()

            # Check if GitHub source already exists
            existing_params = {
                "org_id": organisation_id,
                "source_type": SourceType.GITHUB.value
            }

            existing_result = execute_read_query(source_queries.CHECK_EXISTING_SOURCE, existing_params)
            if existing_result:
                logger.info(f"GitHub source already exists for organisation: {organisation_id}")
                return True, "GitHub source already exists"

            # Create Source node using standard pattern
            source_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            # Use the standard source creation query
            params = {
                "org_id": organisation_id,
                "id": source_id,
                "name": source_name,
                "type": SourceType.GITHUB.value,
                "key": None,  # GitHub token will be set when source is added via SourceService
                "jira_url": None,  # Not applicable for GitHub
                "jira_email": None,  # Not applicable for GitHub
                "is_validated": False,  # Will be validated when credentials are added
                "validation_message": "GitHub source created, awaiting credentials",
                "last_validated_at": None,
                "created_at": current_time,
                "updated_at": current_time
            }

            result = execute_write_query(source_queries.CREATE_SOURCE, params)

            if result:
                logger.info(f"Successfully created GitHub Source node for organisation: {organisation_id}")
                return True, f"GitHub source created successfully with ID: {source_id}"
            else:
                return False, "Failed to create GitHub source node"

        except Exception as e:
            logger.error(f"Error creating GitHub source node: {str(e)}")
            return False, f"Error creating GitHub source: {str(e)}"

    def sync_organizational_access_patterns(self, organisation_id: str):
        """
        Sync organizational access patterns following GDrive patterns.
        This ensures all GitHub entities have proper organizational relationships.
        """
        try:
            logger.info(f"Syncing organizational access patterns for organisation: {organisation_id}")

            params = {
                'organisation_id': organisation_id,
                'granted_at': datetime.now().isoformat()
            }

            # Use the comprehensive organizational sync query
            result = execute_write_query(
                self.relationship_queries.CREATE_GITHUB_USER_DEPARTMENT_ACCESS,
                params
            )

            if result:
                org_count = result[0].get('org_access_count', 0)
                dept_count = result[0].get('dept_access_count', 0)
                user_count = result[0].get('user_access_count', 0)

                logger.info(f"Organizational access sync completed: {org_count} org relationships, "
                           f"{dept_count} department relationships, {user_count} user relationships")

        except Exception as e:
            logger.error(f"Error syncing organizational access patterns: {str(e)}")

    def map_github_users_to_org_users(self, organisation_id: str):
        """
        Map GitHub users to organizational users based on email matching.
        """
        try:
            logger.info(f"Mapping GitHub users to organizational users for organisation: {organisation_id}")

            params = {
                'organisation_id': organisation_id,
                'mapped_at': datetime.now().isoformat()
            }

            result = execute_write_query(
                self.relationship_queries.CREATE_USER_GITHUB_USER_MAPPING,
                params
            )

            if result:
                mappings_created = result[0].get('mappings_created', 0)
                logger.info(f"Created {mappings_created} GitHub user to organizational user mappings")
                return mappings_created

            return 0

        except Exception as e:
            logger.error(f"Error mapping GitHub users to organizational users: {str(e)}")
            return 0

    def check_user_repository_access(self, user_id: str, repository_id: int, organisation_id: str) -> Dict[str, Any]:
        """
        Check if a user has access to a GitHub repository through organizational hierarchy.
        """
        try:
            params = {
                'user_id': user_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id
            }

            result = execute_read_query(
                self.relationship_queries.CHECK_USER_REPOSITORY_ACCESS,
                params
            )

            if result:
                return result[0]

            return {'has_access': False}

        except Exception as e:
            logger.error(f"Error checking user repository access: {str(e)}")
            return {'has_access': False, 'error': str(e)}

    def get_user_accessible_repositories(self, user_id: str, organisation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get repositories accessible to a user through organizational hierarchy.
        """
        try:
            params = {
                'user_id': user_id,
                'organisation_id': organisation_id,
                'limit': limit
            }

            result = execute_read_query(
                self.repository_queries.GET_USER_REPOSITORIES,
                params
            )

            return result if result else []

        except Exception as e:
            logger.error(f"Error getting user accessible repositories: {str(e)}")
            return []

    def get_department_accessible_repositories(self, department_id: str, organisation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get repositories accessible to a department.
        """
        try:
            params = {
                'department_id': department_id,
                'organisation_id': organisation_id,
                'limit': limit
            }

            result = execute_read_query(
                self.repository_queries.GET_USER_REPOSITORIES,
                params
            )

            return result if result else []

        except Exception as e:
            logger.error(f"Error getting department accessible repositories: {str(e)}")
            return []

    def sync_github(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int, int]:
        """
        Perform a full sync of GitHub data for an organisation.
        
        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync
            
        Returns:
            Tuple of (success, message, repositories_synced, total_items_synced)
        """
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for GitHub sync")
                return False, "organisation_id is required", 0, 0

            logger.info(f"Starting GitHub sync for organisation: {organisation_id}")

            # Step 1: Create GitHub Source node (mirroring GDrive's source creation pattern)
            source_success, source_message = self.create_github_source_node(organisation_id)
            if not source_success:
                logger.warning(f"GitHub source creation warning: {source_message}")

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0, 0

            # Use the new sync service for organization-level sync
            success, message, items_synced = self.sync_service.sync_organization(organisation_id, full_sync)

            if success:
                # Update last sync time
                self._update_last_sync_time(organisation_id)

                # For backward compatibility, estimate repositories synced
                repositories_synced = max(1, items_synced // 10)  # Rough estimate
                total_items_synced = items_synced

                logger.info(f"GitHub sync completed: {repositories_synced} repositories, {total_items_synced} total items")
                return True, f"Successfully synced {repositories_synced} repositories. {source_message}", repositories_synced, total_items_synced
            else:
                return False, message, 0, 0

            
        except Exception as e:
            logger.error(f"Error during GitHub sync: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0, 0

    def sync_github_user(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Perform user-level GitHub sync using modular components.

        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for GitHub user sync")
                return False, "organisation_id is required", 0

            logger.info(f"Starting GitHub user sync for organisation: {organisation_id}")

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0

            # Use the new sync service for user-level sync
            success, message, items_synced = self.sync_service.sync_user(organisation_id, full_sync)

            if success:
                # Update last sync time
                self._update_last_sync_time(organisation_id)

                logger.info(f"GitHub user sync completed: {items_synced} items")
                return True, message, items_synced
            else:
                return False, message, 0

        except Exception as e:
            logger.error(f"Error in GitHub user sync: {str(e)}")
            return False, f"User sync failed: {str(e)}", 0

    def create_github_source_node(self, organisation_id: str) -> Tuple[bool, str]:
        """Create GitHub source node following Google Drive patterns."""
        try:
            source_queries = SourceQueries()

            # Check if GitHub source already exists
            existing_source = execute_read_query(
                source_queries.GET_SOURCE_BY_TYPE_AND_ORGANISATION,
                {'organisation_id': organisation_id, 'source_type': SourceType.GITHUB.value}
            )

            if existing_source:
                logger.info(f"GitHub source already exists for organisation {organisation_id}")
                return True, "GitHub source already exists"

            # Create new GitHub source
            source_params = {
                'source_id': str(uuid.uuid4()),
                'organisation_id': organisation_id,
                'source_type': SourceType.GITHUB.value,
                'source_name': 'GitHub',
                'is_active': True,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }

            execute_write_query(source_queries.CREATE_SOURCE, source_params)
            logger.info(f"Created GitHub source for organisation {organisation_id}")
            return True, "GitHub source created successfully"

        except Exception as e:
            logger.error(f"Error creating GitHub source node: {str(e)}")
            return False, f"Failed to create GitHub source: {str(e)}"

    def _update_last_sync_time(self, organisation_id: str):
        """Update last sync time following Google Drive patterns."""
        try:
            source_queries = SourceQueries()
            params = {
                'organisation_id': organisation_id,
                'source_type': SourceType.GITHUB.value,
                'last_sync_at': datetime.utcnow().isoformat()
            }
            execute_write_query(source_queries.UPDATE_SOURCE_LAST_SYNC, params)
            logger.debug(f"Updated last sync time for organisation {organisation_id}")
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")

    def search_github_content(self, query: str, organisation_id: str, limit: int = 10) -> List[Dict]:
        """
        Search GitHub content using hybrid search engine.

        Args:
            query: Search query
            organisation_id: Organisation ID
            limit: Maximum number of results

        Returns:
            List of search results
        """
        try:
            # Validate required parameters
            if not organisation_id:
                logger.error("organisation_id is required for searching GitHub content")
                return []

            if not query or not query.strip():
                logger.error("query is required for searching GitHub content")
                return []

            if limit <= 0:
                logger.warning("Invalid limit provided, using default limit of 10")
                limit = 10

            # Use hybrid search engine for GitHub content
            search_engine = HybridSearchEngine()

            # Define GitHub entity types for search
            entity_types = [
                'GitHubRepository',
                'GitHubIssue',
                'GitHubPullRequest',
                'GitHubCodeFile'
            ]

            results = search_engine.search(
                query=query,
                organisation_id=organisation_id,
                entity_types=entity_types,
                limit=limit
            )

            return results

        except Exception as e:
            logger.error(f"Error searching GitHub content: {str(e)}")
            return []

    def get_user_accessible_repositories(self, user_id: str, organisation_id: str) -> List[Dict]:
        """
        Get repositories accessible by a specific user following Google Drive patterns.

        Args:
            user_id: User ID
            organisation_id: Organisation ID

        Returns:
            List of accessible repositories
        """
        try:
            # Use relationship queries to find accessible repositories
            params = {
                'user_id': user_id,
                'organisation_id': organisation_id
            }

            results = execute_read_query(
                self.relationship_queries.GET_USER_ACCESSIBLE_REPOSITORIES,
                params
            )

            return [dict(record) for record in results] if results else []

        except Exception as e:
            logger.error(f"Error getting user accessible repositories: {str(e)}")
            return []

    def extract_repo_info_from_url(self, github_url: str) -> Optional[Dict[str, str]]:
        """
        Extract repository owner and name from a GitHub URL.

        Args:
            github_url: The GitHub URL

        Returns:
            Dictionary with owner and repo name or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://github.com/owner/repo
            # Format 2: https://github.com/owner/repo/issues/123
            # Format 3: https://github.com/owner/repo/pull/456

            if "github.com/" in github_url:
                # Extract owner/repo from URL
                parts = github_url.split("github.com/")[1].split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                    return {"owner": owner, "repo": repo}

            logger.error(f"Unsupported GitHub URL format: {github_url}")
            return None

        except Exception as e:
            logger.error(f"Error extracting repo info from URL: {str(e)}")
            return None

    def _schedule_sync(
        self,
        user_id: str,
        organisation_id: str = None,
        full_sync: bool = False,
        delay_seconds: int = 0) -> str:
        """
        Schedule a GitHub sync job.

        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organisation (required)
            full_sync: Whether to perform a full sync
            delay_seconds: Delay before executing the job

        Returns:
            The job ID

        Raises:
            ValueError: If organisation_id is None
        """
        # organisation_id is required for sync jobs
        if organisation_id is None:
            raise ValueError("organisation_id is required for scheduling sync jobs")

        import time
        from datetime import timedelta

        job_data = {
            'job_type': 'full_sync' if full_sync else 'incremental_sync',
            'user_id': user_id,
            'organisation_id': organisation_id,
            'full_sync': full_sync,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }

        # Store in Redis with appropriate expiration
        job_id = f"github_sync:{user_id}:{int(time.time())}"
        self.redis_service.set(
            job_id,
            json.dumps(job_data),
            ex=86400)  # 24 hour expiration

        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("github_sync_queue", {job_id: score})

        return job_id

    def _cancel_scheduled_syncs(self, user_id: str) -> None:
        """
        Cancel all scheduled syncs for a user.

        Args:
            user_id: The ID of the user
        """
        # Find all sync jobs for this user
        pattern = f"github_sync:{user_id}:*"
        keys = self.redis_service.keys(pattern)

        # Remove from sorted set and delete keys
        for key in keys:
            self.redis_service.zrem("github_sync_queue", key)
            self.redis_service.delete(key)

    def _cancel_scheduled_syncs_for_organization(self, organisation_id: str) -> None:
        """
        Cancel all scheduled syncs for an organization.

        Args:
            organisation_id: The ID of the organization
        """
        try:
            # Find all sync jobs for this organization
            pattern = f"github_sync:*:{organisation_id}:*"
            keys = self.redis_service.keys(pattern)

            # Remove each job from the queue and delete the job data
            for key in keys:
                self.redis_service.zrem("github_sync_queue", key)
                self.redis_service.delete(key)

        except Exception as e:
            logger.error(f"Error canceling scheduled GitHub syncs: {str(e)}")

    def _fetch_github_organizations(self, session: requests.Session) -> List[Dict]:
        """Fetch GitHub organizations for the authenticated user with comprehensive data."""
        try:
            url = "https://api.github.com/user/orgs"
            response = session.get(url)
            response.raise_for_status()

            organizations = []
            for org in response.json():
                # Fetch detailed organization info
                org_url = f"https://api.github.com/orgs/{org['login']}"
                org_response = session.get(org_url)
                if org_response.status_code == 200:
                    detailed_org = org_response.json()
                    
                    # Fetch organization members
                    members = self._fetch_organization_members(session, org['login'])
                    detailed_org['members'] = members
                    
                    # Fetch organization teams
                    teams = self._fetch_organization_teams(session, org['login'])
                    detailed_org['teams'] = teams
                    
                    # Fetch organization repositories
                    org_repos = self._fetch_organization_repositories(session, org['login'])
                    detailed_org['repositories'] = org_repos
                    
                    organizations.append(detailed_org)
                else:
                    # Use basic org info if detailed fetch fails
                    organizations.append(org)

            return organizations
        except Exception as e:
            logger.error(f"Failed to fetch GitHub organizations: {str(e)}")
            return []

    def _fetch_organization_members(self, session: requests.Session, org_login: str) -> List[Dict]:
        """Fetch all members of a GitHub organization."""
        try:
            members = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/orgs/{org_login}/members"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_members = response.json()
                    if not page_members:
                        break
                    
                    # Fetch detailed info for each member
                    for member in page_members:
                        member_url = f"https://api.github.com/users/{member['login']}"
                        member_response = session.get(member_url)
                        if member_response.status_code == 200:
                            detailed_member = member_response.json()
                            # Add organization role info
                            detailed_member['org_role'] = 'member'  # Default role
                            members.append(detailed_member)
                        else:
                            members.append(member)
                    
                    page += 1
                else:
                    logger.warning(f"Failed to fetch members for org {org_login}: {response.status_code}")
                    break
            
            logger.info(f"Fetched {len(members)} members for organization {org_login}")
            return members
            
        except Exception as e:
            logger.error(f"Failed to fetch organization members for {org_login}: {str(e)}")
            return []

    def _fetch_organization_teams(self, session: requests.Session, org_login: str) -> List[Dict]:
        """Fetch all teams of a GitHub organization."""
        try:
            teams = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/orgs/{org_login}/teams"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_teams = response.json()
                    if not page_teams:
                        break
                    
                    # Fetch detailed info for each team including members
                    for team in page_teams:
                        # Fetch team members
                        team_members = self._fetch_team_members(session, org_login, team['slug'])
                        team['members'] = team_members
                        
                        # Fetch team repositories
                        team_repos = self._fetch_team_repositories(session, org_login, team['slug'])
                        team['repositories'] = team_repos
                        
                        teams.append(team)
                    
                    page += 1
                else:
                    logger.warning(f"Failed to fetch teams for org {org_login}: {response.status_code}")
                    break
            
            logger.info(f"Fetched {len(teams)} teams for organization {org_login}")
            return teams
            
        except Exception as e:
            logger.error(f"Failed to fetch organization teams for {org_login}: {str(e)}")
            return []

    def _fetch_team_members(self, session: requests.Session, org_login: str, team_slug: str) -> List[Dict]:
        """Fetch members of a specific team."""
        try:
            members = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/members"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_members = response.json()
                    if not page_members:
                        break
                    
                    members.extend(page_members)
                    page += 1
                else:
                    break
            
            return members
            
        except Exception as e:
            logger.error(f"Failed to fetch team members for {org_login}/{team_slug}: {str(e)}")
            return []

    def _fetch_team_repositories(self, session: requests.Session, org_login: str, team_slug: str) -> List[Dict]:
        """Fetch repositories accessible by a specific team."""
        try:
            repositories = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/repos"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_repos = response.json()
                    if not page_repos:
                        break
                    
                    repositories.extend(page_repos)
                    page += 1
                else:
                    break
            
            return repositories
            
        except Exception as e:
            logger.error(f"Failed to fetch team repositories for {org_login}/{team_slug}: {str(e)}")
            return []

    def _fetch_organization_repositories(self, session: requests.Session, org_login: str) -> List[Dict]:
        """Fetch all repositories of a GitHub organization."""
        try:
            repositories = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/orgs/{org_login}/repos"
                params = {
                    'page': page,
                    'per_page': per_page,
                    'type': 'all',  # all, public, private, forks, sources, member
                    'sort': 'updated',
                    'direction': 'desc'
                }
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_repos = response.json()
                    if not page_repos:
                        break
                    
                    repositories.extend(page_repos)
                    page += 1
                else:
                    logger.warning(f"Failed to fetch repositories for org {org_login}: {response.status_code}")
                    break
            
            logger.info(f"Fetched {len(repositories)} repositories for organization {org_login}")
            return repositories
            
        except Exception as e:
            logger.error(f"Failed to fetch organization repositories for {org_login}: {str(e)}")
            return []

    def _fetch_user_repositories(self, session: requests.Session) -> List[Dict]:
        """Fetch user's repositories from GitHub API with comprehensive data."""
        try:
            repositories = []
            page = 1
            per_page = 100
            
            while True:
                url = "https://api.github.com/user/repos"
                params = {
                    'type': 'all',
                    'sort': 'updated',
                    'per_page': per_page,
                    'page': page
                }
                response = session.get(url, params=params)
                response.raise_for_status()
                
                page_repos = response.json()
                if not page_repos:
                    break
                
                # Enhance each repository with comprehensive data
                for repo in page_repos:
                    enhanced_repo = self._enhance_repository_data(session, repo)
                    repositories.append(enhanced_repo)
                
                page += 1
            
            logger.info(f"Fetched {len(repositories)} user repositories")
            return repositories
            
        except Exception as e:
            logger.error(f"Failed to fetch user repositories: {str(e)}")
            return []

    def _enhance_repository_data(self, session: requests.Session, repo_data: Dict) -> Dict:
        """Enhance repository data with comprehensive information."""
        try:
            owner = repo_data['owner']['login']
            repo_name = repo_data['name']
            
            # Add branches
            repo_data['branches'] = self._fetch_repository_branches(session, owner, repo_name)
            
            # Add tags
            repo_data['tags'] = self._fetch_repository_tags(session, owner, repo_name)
            
            # Add releases
            repo_data['releases'] = self._fetch_repository_releases(session, owner, repo_name)
            
            # Add collaborators
            repo_data['collaborators'] = self._fetch_repository_collaborators(session, owner, repo_name)
            
            # Add workflows
            repo_data['workflows'] = self._fetch_repository_workflows(session, owner, repo_name)
            
            # Add file structure (for documentation processing)
            repo_data['file_structure'] = self._fetch_repository_file_structure(session, owner, repo_name)
            
            return repo_data
            
        except Exception as e:
            logger.error(f"Failed to enhance repository data for {repo_data.get('full_name', 'unknown')}: {str(e)}")
            return repo_data

    def _fetch_repository_branches(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all branches of a repository."""
        try:
            branches = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/branches"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_branches = response.json()
                    if not page_branches:
                        break
                    
                    # Enhance each branch with protection info
                    for branch in page_branches:
                        # Fetch branch protection info
                        protection_url = f"https://api.github.com/repos/{owner}/{repo}/branches/{branch['name']}/protection"
                        protection_response = session.get(protection_url)
                        if protection_response.status_code == 200:
                            branch['protection'] = protection_response.json()
                        else:
                            branch['protection'] = None
                    
                    branches.extend(page_branches)
                    page += 1
                else:
                    break
            
            return branches
            
        except Exception as e:
            logger.error(f"Failed to fetch branches for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_tags(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all tags of a repository."""
        try:
            tags = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/tags"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_tags = response.json()
                    if not page_tags:
                        break
                    
                    tags.extend(page_tags)
                    page += 1
                else:
                    break
            
            return tags
            
        except Exception as e:
            logger.error(f"Failed to fetch tags for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_releases(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all releases of a repository."""
        try:
            releases = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/releases"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_releases = response.json()
                    if not page_releases:
                        break
                    
                    releases.extend(page_releases)
                    page += 1
                else:
                    break
            
            return releases
            
        except Exception as e:
            logger.error(f"Failed to fetch releases for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_collaborators(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all collaborators of a repository."""
        try:
            collaborators = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/collaborators"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_collaborators = response.json()
                    if not page_collaborators:
                        break
                    
                    collaborators.extend(page_collaborators)
                    page += 1
                else:
                    break
            
            return collaborators
            
        except Exception as e:
            logger.error(f"Failed to fetch collaborators for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_workflows(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all GitHub Actions workflows of a repository."""
        try:
            workflows = []
            url = f"https://api.github.com/repos/{owner}/{repo}/actions/workflows"
            response = session.get(url)
            
            if response.status_code == 200:
                workflows_data = response.json()
                workflows = workflows_data.get('workflows', [])
                
                # Fetch recent runs for each workflow
                for workflow in workflows:
                    runs_url = f"https://api.github.com/repos/{owner}/{repo}/actions/workflows/{workflow['id']}/runs"
                    runs_response = session.get(runs_url, params={'per_page': 10})
                    if runs_response.status_code == 200:
                        workflow['recent_runs'] = runs_response.json().get('workflow_runs', [])
            
            return workflows
            
        except Exception as e:
            logger.error(f"Failed to fetch workflows for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_file_structure(self, session: requests.Session, owner: str, repo: str, path: str = "") -> List[Dict]:
        """Fetch repository file structure for documentation processing."""
        try:
            files = []
            url = f"https://api.github.com/repos/{owner}/{repo}/contents/{path}"
            response = session.get(url)
            
            if response.status_code == 200:
                contents = response.json()
                
                for item in contents:
                    if item['type'] == 'file':
                        # Check if it's a documentation file that needs processing
                        file_extension = self._get_file_extension(item['name'])
                        if file_extension in ['.md', '.pdf', '.txt', '.rst', '.adoc', '.json', '.yaml', '.yml']:
                            item['needs_processing'] = True
                            item['processing_type'] = 'documentation' if file_extension in ['.md', '.pdf', '.txt', '.rst', '.adoc'] else 'configuration'
                        else:
                            item['needs_processing'] = False
                            item['processing_type'] = 'code_metadata'
                        
                        files.append(item)
                    elif item['type'] == 'dir':
                        # Add directory info
                        files.append(item)
                        # Recursively fetch subdirectory contents (limit depth to avoid API limits)
                        if path.count('/') < 3:  # Limit recursion depth
                            subfiles = self._fetch_repository_file_structure(session, owner, repo, item['path'])
                            files.extend(subfiles)
            
            return files
            
        except Exception as e:
            logger.error(f"Failed to fetch file structure for {owner}/{repo}/{path}: {str(e)}")
            return []

    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename."""
        if '.' in filename:
            return '.' + filename.split('.')[-1].lower()
        return ''

    def _fetch_repository_issues_comprehensive(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all issues of a repository with comprehensive data including comments."""
        try:
            issues = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/issues"
                params = {
                    'state': 'all',
                    'page': page,
                    'per_page': per_page,
                    'sort': 'updated',
                    'direction': 'desc'
                }
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_issues = response.json()
                    if not page_issues:
                        break
                    
                    # Filter out pull requests (GitHub API returns PRs as issues)
                    actual_issues = [issue for issue in page_issues if 'pull_request' not in issue]
                    
                    # Enhance each issue with comments
                    for issue in actual_issues:
                        # Fetch issue comments
                        comments = self._fetch_issue_comments_comprehensive(session, owner, repo, issue['number'])
                        issue['comments_data'] = comments
                        
                        issues.append(issue)
                    
                    page += 1
                else:
                    break
            
            logger.info(f"Fetched {len(issues)} issues for {owner}/{repo}")
            return issues
            
        except Exception as e:
            logger.error(f"Failed to fetch comprehensive issues for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_issue_comments_comprehensive(self, session: requests.Session, owner: str, repo: str, issue_number: int) -> List[Dict]:
        """Fetch all comments for a specific issue."""
        try:
            comments = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/issues/{issue_number}/comments"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_comments = response.json()
                    if not page_comments:
                        break
                    
                    comments.extend(page_comments)
                    page += 1
                else:
                    break
            
            return comments
            
        except Exception as e:
            logger.error(f"Failed to fetch comments for issue {owner}/{repo}#{issue_number}: {str(e)}")
            return []

    def _fetch_repository_pull_requests_comprehensive(self, session: requests.Session, owner: str, repo: str) -> List[Dict]:
        """Fetch all pull requests of a repository with comprehensive data including reviews and comments."""
        try:
            pull_requests = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
                params = {
                    'state': 'all',
                    'page': page,
                    'per_page': per_page,
                    'sort': 'updated',
                    'direction': 'desc'
                }
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_prs = response.json()
                    if not page_prs:
                        break
                    
                    # Enhance each PR with comprehensive data
                    for pr in page_prs:
                        # Fetch PR comments
                        comments = self._fetch_pr_comments_comprehensive(session, owner, repo, pr['number'])
                        pr['comments_data'] = comments
                        
                        # Fetch PR reviews
                        reviews = self._fetch_pr_reviews_comprehensive(session, owner, repo, pr['number'])
                        pr['reviews_data'] = reviews
                        
                        # Fetch PR review comments
                        review_comments = self._fetch_pr_review_comments_comprehensive(session, owner, repo, pr['number'])
                        pr['review_comments_data'] = review_comments
                        
                        pull_requests.append(pr)
                    
                    page += 1
                else:
                    break
            
            logger.info(f"Fetched {len(pull_requests)} pull requests for {owner}/{repo}")
            return pull_requests
            
        except Exception as e:
            logger.error(f"Failed to fetch comprehensive pull requests for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_pr_comments_comprehensive(self, session: requests.Session, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Fetch all comments for a specific pull request."""
        try:
            comments = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/issues/{pr_number}/comments"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_comments = response.json()
                    if not page_comments:
                        break
                    
                    comments.extend(page_comments)
                    page += 1
                else:
                    break
            
            return comments
            
        except Exception as e:
            logger.error(f"Failed to fetch comments for PR {owner}/{repo}#{pr_number}: {str(e)}")
            return []

    def _fetch_pr_reviews_comprehensive(self, session: requests.Session, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Fetch all reviews for a specific pull request."""
        try:
            reviews = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/reviews"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_reviews = response.json()
                    if not page_reviews:
                        break
                    
                    reviews.extend(page_reviews)
                    page += 1
                else:
                    break
            
            return reviews
            
        except Exception as e:
            logger.error(f"Failed to fetch reviews for PR {owner}/{repo}#{pr_number}: {str(e)}")
            return []

    def _fetch_pr_review_comments_comprehensive(self, session: requests.Session, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Fetch all review comments for a specific pull request."""
        try:
            review_comments = []
            page = 1
            per_page = 100
            
            while True:
                url = f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/comments"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_comments = response.json()
                    if not page_comments:
                        break
                    
                    review_comments.extend(page_comments)
                    page += 1
                else:
                    break
            
            return review_comments
            
        except Exception as e:
            logger.error(f"Failed to fetch review comments for PR {owner}/{repo}#{pr_number}: {str(e)}")
            return []

    def _fetch_repository_commits_comprehensive(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository commits with comprehensive data including comments."""
        try:
            commits = []
            page = 1
            per_page = min(limit, 100)
            
            while len(commits) < limit:
                url = f"https://api.github.com/repos/{owner}/{repo}/commits"
                params = {'page': page, 'per_page': per_page}
                response = session.get(url, params=params)
                
                if response.status_code == 200:
                    page_commits = response.json()
                    if not page_commits:
                        break
                    
                    # Enhance each commit with comments
                    for commit in page_commits:
                        if len(commits) >= limit:
                            break
                        
                        # Fetch commit comments
                        comments = self._fetch_commit_comments(session, owner, repo, commit['sha'])
                        commit['comments_data'] = comments
                        
                        commits.append(commit)
                    
                    page += 1
                else:
                    break
            
            logger.info(f"Fetched {len(commits)} commits for {owner}/{repo}")
            return commits
            
        except Exception as e:
            logger.error(f"Failed to fetch comprehensive commits for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_commit_comments(self, session: requests.Session, owner: str, repo: str, commit_sha: str) -> List[Dict]:
        """Fetch all comments for a specific commit."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_sha}/comments"
            response = session.get(url)
            
            if response.status_code == 200:
                return response.json()
            else:
                return []
            
        except Exception as e:
            logger.error(f"Failed to fetch comments for commit {owner}/{repo}/{commit_sha}: {str(e)}")
            return []

    def _process_documentation_files(self, session: requests.Session, file_structure: List[Dict],
                                   repository_id: int, organisation_id: str) -> List[Dict]:
        """Process documentation files with content extraction and chunking like Google Drive."""
        try:
            processed_files = []
            text_extractor = TextExtractor()
            chunking_engine = ChunkingEngine(chunk_size=1000, chunk_overlap=200)
            
            for file_item in file_structure:
                if file_item.get('type') == 'file' and file_item.get('needs_processing'):
                    processing_type = file_item.get('processing_type')
                    
                    if processing_type == 'documentation':
                        # Process documentation files with full content extraction and chunking
                        processed_file = self._process_documentation_file_with_chunking(
                            session, file_item, repository_id, organisation_id,
                            text_extractor, chunking_engine
                        )
                        processed_files.append(processed_file)
                        
                    elif processing_type == 'configuration':
                        # Process configuration files with content extraction only
                        processed_file = self._process_configuration_file(
                            session, file_item, repository_id, organisation_id, text_extractor
                        )
                        processed_files.append(processed_file)
                        
                    else:
                        # Code files - metadata only
                        processed_file = self._process_code_file_metadata_only(
                            file_item, repository_id, organisation_id
                        )
                        processed_files.append(processed_file)
            
            return processed_files
            
        except Exception as e:
            logger.error(f"Failed to process documentation files: {str(e)}")
            return []

    def _process_documentation_file_with_chunking(self, session: requests.Session, file_item: Dict,
                                                repository_id: int, organisation_id: str,
                                                text_extractor: TextExtractor,
                                                chunking_engine: ChunkingEngine) -> Dict:
        """Process documentation file with content extraction and chunking like Google Drive."""
        try:
            # Download file content
            file_content = self._download_github_file_content(session, file_item['download_url'])
            if not file_content:
                return self._create_file_metadata_only(file_item, repository_id, organisation_id)
            
            # Determine MIME type from file extension
            file_extension = self._get_file_extension(file_item['name'])
            mime_type = self._get_mime_type_from_extension(file_extension)
            
            # Extract text content
            extracted_text = text_extractor.extract_text(file_content, mime_type)
            if not extracted_text:
                return self._create_file_metadata_only(file_item, repository_id, organisation_id)
            
            # Create base file node
            file_node = self._create_github_code_file_node(file_item, repository_id, organisation_id)
            
            # Create chunks using ChunkingEngine
            base_chunk_id = f"github_{repository_id}_{file_item['sha']}"
            chunks = chunking_engine.create_chunks_from_text(extracted_text, base_chunk_id)
            
            # Create GitHubTextChunk nodes for each chunk
            chunk_nodes = []
            for chunk in chunks:
                chunk_node = self._create_github_text_chunk_node(
                    chunk, file_item, repository_id, organisation_id
                )
                chunk_nodes.append(chunk_node)
                
                # Create CHUNK_OF_FILE relationship
                self._create_chunk_of_file_relationship(chunk_node['chunk_id'], file_node['file_id'])
            
            # Vectorize chunks and store in Pinecone
            self._vectorize_documentation_chunks(chunk_nodes, file_item, extracted_text)
            
            # Update file node with processing info
            file_node['content_processed'] = True
            file_node['chunks_created'] = len(chunk_nodes)
            file_node['content_length'] = len(extracted_text)
            
            return file_node
            
        except Exception as e:
            logger.error(f"Failed to process documentation file {file_item.get('name', 'unknown')}: {str(e)}")
            return self._create_file_metadata_only(file_item, repository_id, organisation_id)

    def _process_configuration_file(self, session: requests.Session, file_item: Dict,
                                  repository_id: int, organisation_id: str,
                                  text_extractor: TextExtractor) -> Dict:
        """Process configuration file with content extraction only (no chunking)."""
        try:
            # Download file content
            file_content = self._download_github_file_content(session, file_item['download_url'])
            if not file_content:
                return self._create_file_metadata_only(file_item, repository_id, organisation_id)
            
            # Determine MIME type from file extension
            file_extension = self._get_file_extension(file_item['name'])
            mime_type = self._get_mime_type_from_extension(file_extension)
            
            # Extract text content
            extracted_text = text_extractor.extract_text(file_content, mime_type)
            
            # Create file node with content
            file_node = self._create_github_code_file_node(file_item, repository_id, organisation_id)
            
            if extracted_text:
                # Store content in file node (for configuration files, we store the content)
                file_node['content'] = extracted_text[:5000]  # Limit content size
                file_node['content_processed'] = True
                file_node['content_length'] = len(extracted_text)
            else:
                file_node['content_processed'] = False
            
            return file_node
            
        except Exception as e:
            logger.error(f"Failed to process configuration file {file_item.get('name', 'unknown')}: {str(e)}")
            return self._create_file_metadata_only(file_item, repository_id, organisation_id)

    def _process_code_file_metadata_only(self, file_item: Dict, repository_id: int, organisation_id: str) -> Dict:
        """Process code file with metadata only (no content extraction)."""
        return self._create_file_metadata_only(file_item, repository_id, organisation_id)

    def _download_github_file_content(self, session: requests.Session, download_url: str) -> bytes:
        """Download file content from GitHub."""
        try:
            response = session.get(download_url)
            if response.status_code == 200:
                return response.content
            else:
                logger.warning(f"Failed to download file content: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading file content: {str(e)}")
            return None

    def _get_mime_type_from_extension(self, file_extension: str) -> str:
        """Get MIME type from file extension."""
        mime_type_mapping = {
            '.md': 'text/markdown',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.rst': 'text/x-rst',
            '.adoc': 'text/asciidoc',
            '.json': 'application/json',
            '.yaml': 'text/yaml',
            '.yml': 'text/yaml',
            '.xml': 'application/xml',
            '.html': 'text/html',
            '.htm': 'text/html'
        }
        return mime_type_mapping.get(file_extension, 'text/plain')

    def _create_file_metadata_only(self, file_item: Dict, repository_id: int, organisation_id: str) -> Dict:
        """Create file node with metadata only."""
        return {
            'file_id': f"github_file_{file_item['sha']}",
            'path': file_item['path'],
            'name': file_item['name'],
            'size': file_item.get('size', 0),
            'sha': file_item['sha'],
            'html_url': file_item.get('html_url', ''),
            'download_url': file_item.get('download_url', ''),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'content_type': self._get_content_type_from_extension(file_item['name']),
            'content_processed': False,
            'created_at': datetime.now().isoformat()
        }

    def _create_github_code_file_node(self, file_item: Dict, repository_id: int, organisation_id: str) -> Dict:
        """Create GitHubCodeFile node in Neo4j."""
        try:
            file_data = {
                'path': file_item['path'],
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'sha': file_item['sha'],
                'name': file_item['name'],
                'size': file_item.get('size', 0),
                'html_url': file_item.get('html_url', ''),
                'download_url': file_item.get('download_url', ''),
                'content_type': self._get_content_type_from_extension(file_item['name']),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # Create file node using existing query
            execute_write_query(self.file_queries.CREATE_OR_UPDATE_FILE, file_data)
            
            return {
                'file_id': f"github_file_{file_item['sha']}",
                **file_data
            }
            
        except Exception as e:
            logger.error(f"Failed to create GitHub code file node: {str(e)}")
            return self._create_file_metadata_only(file_item, repository_id, organisation_id)

    def _create_github_text_chunk_node(self, chunk, file_item: Dict, repository_id: int, organisation_id: str) -> Dict:
        """Create GitHubTextChunk node in Neo4j."""
        try:
            chunk_data = {
                'id': chunk.metadata.chunk_id,
                'organisation_id': organisation_id,
                'file_path': file_item['path'],
                'repository_id': repository_id,
                'content': chunk.text,
                'start_line': chunk.metadata.start_position,
                'end_line': chunk.metadata.end_position,
                'vector_id': str(uuid.uuid4()),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # Create chunk node (we'll need to add this query to the queries file)
            # For now, we'll create it directly
            chunk_query = """
            MERGE (chunk:GitHubTextChunk {id: $id})
            SET chunk.organisation_id = $organisation_id,
                chunk.file_path = $file_path,
                chunk.repository_id = $repository_id,
                chunk.content = $content,
                chunk.start_line = $start_line,
                chunk.end_line = $end_line,
                chunk.vector_id = $vector_id,
                chunk.created_at = $created_at,
                chunk.updated_at = $updated_at
            RETURN chunk
            """
            
            execute_write_query(chunk_query, chunk_data)
            
            return {
                'chunk_id': chunk.metadata.chunk_id,
                **chunk_data
            }
            
        except Exception as e:
            logger.error(f"Failed to create GitHub text chunk node: {str(e)}")
            return None

    def _create_chunk_of_file_relationship(self, chunk_id: str, file_id: str):
        """Create CHUNK_OF_FILE relationship between chunk and file."""
        try:
            relationship_query = """
            MATCH (chunk:GitHubTextChunk {id: $chunk_id})
            MATCH (file:GitHubCodeFile {path: $file_path})
            MERGE (chunk)-[r:CHUNK_OF_FILE]->(file)
            SET r.created_at = $created_at
            RETURN r
            """
            
            params = {
                'chunk_id': chunk_id,
                'file_path': file_id,  # Using file path as identifier
                'created_at': datetime.now().isoformat()
            }
            
            execute_write_query(relationship_query, params)
            
        except Exception as e:
            logger.error(f"Failed to create chunk-file relationship: {str(e)}")

    def _vectorize_documentation_chunks(self, chunk_nodes: List[Dict], file_item: Dict, full_text: str):
        """Vectorize documentation chunks and store in Pinecone like Google Drive."""
        try:
            for chunk_node in chunk_nodes:
                if chunk_node:
                    # Create vector data similar to Google Drive
                    vector_data = {
                        'id': chunk_node['vector_id'],
                        'text': chunk_node['content'],
                        'metadata': {
                            'source': 'github',
                            'type': 'documentation',
                            'file_path': file_item['path'],
                            'file_name': file_item['name'],
                            'repository_id': chunk_node['repository_id'],
                            'organisation_id': chunk_node['organisation_id'],
                            'chunk_id': chunk_node['chunk_id'],
                            'file_url': file_item.get('html_url', ''),
                            'created_at': chunk_node['created_at']
                        }
                    }
                    
                    # Store in Pinecone using existing service
                    self.pinecone_service.upsert_vectors([vector_data])
                    
        except Exception as e:
            logger.error(f"Failed to vectorize documentation chunks: {str(e)}")

    def _get_content_type_from_extension(self, filename: str) -> str:
        """Determine content type from file extension."""
        file_extension = self._get_file_extension(filename)
        
        if file_extension in ['.md', '.rst', '.adoc', '.txt']:
            return 'documentation'
        elif file_extension in ['.json', '.yaml', '.yml', '.xml', '.toml', '.ini']:
            return 'configuration'
        elif file_extension in ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.go', '.rs']:
            return 'code'
        else:
            return 'unknown'

    def _sync_repository_comprehensive(self, session: requests.Session, repo_data: Dict,
                                     organisation_id: str, full_sync: bool = False) -> int:
        """Sync a repository with all its comprehensive data."""
        try:
            items_synced = 0
            owner = repo_data['owner']['login']
            repo_name = repo_data['name']
            
            # 1. Create/update repository node
            self._create_or_update_repository(repo_data, organisation_id)
            items_synced += 1
            
            # 2. Sync branches
            for branch in repo_data.get('branches', []):
                self._create_or_update_branch(branch, repo_data['id'], organisation_id)
                items_synced += 1
            
            # 3. Sync tags
            for tag in repo_data.get('tags', []):
                self._create_or_update_tag(tag, repo_data['id'], organisation_id)
                items_synced += 1
            
            # 4. Sync releases
            for release in repo_data.get('releases', []):
                self._create_or_update_release(release, repo_data['id'], organisation_id)
                items_synced += 1
            
            # 5. Sync collaborators
            for collaborator in repo_data.get('collaborators', []):
                self._create_or_update_user(collaborator, organisation_id)
                self._create_repository_collaborator_relationship(collaborator, repo_data, organisation_id)
                items_synced += 1
            
            # 6. Sync workflows
            for workflow in repo_data.get('workflows', []):
                self._create_or_update_workflow(workflow, repo_data['id'], organisation_id)
                items_synced += 1
            
            # 7. Process file structure and documentation
            file_structure = repo_data.get('file_structure', [])
            processed_files = self._process_documentation_files(session, file_structure, repo_data['id'], organisation_id)
            items_synced += len(processed_files)
            
            if full_sync:
                # 8. Sync issues with comprehensive data
                issues = self._fetch_repository_issues_comprehensive(session, owner, repo_name)
                for issue in issues:
                    self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                    items_synced += 1
                    
                    # Sync issue comments
                    for comment in issue.get('comments_data', []):
                        self._create_or_update_comment(comment, organisation_id, issue_id=issue['id'])
                        items_synced += 1
                
                # 9. Sync pull requests with comprehensive data
                pull_requests = self._fetch_repository_pull_requests_comprehensive(session, owner, repo_name)
                for pr in pull_requests:
                    self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                    items_synced += 1
                    
                    # Sync PR comments
                    for comment in pr.get('comments_data', []):
                        self._create_or_update_comment(comment, organisation_id, pull_request_id=pr['id'])
                        items_synced += 1
                    
                    # Sync PR reviews
                    for review in pr.get('reviews_data', []):
                        self._create_or_update_review(review, organisation_id, pr['id'])
                        items_synced += 1
                    
                    # Sync PR review comments
                    for review_comment in pr.get('review_comments_data', []):
                        self._create_or_update_review_comment(review_comment, organisation_id, pr['id'])
                        items_synced += 1
            
            # 10. Sync commits (always sync recent commits)
            commit_limit = 100 if full_sync else 20
            commits = self._fetch_repository_commits_comprehensive(session, owner, repo_name, limit=commit_limit)
            for commit in commits:
                self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                items_synced += 1
                
                # Sync commit comments
                for comment in commit.get('comments_data', []):
                    self._create_or_update_comment(comment, organisation_id, commit_sha=commit['sha'])
                    items_synced += 1
            
            # 11. Create organizational relationships
            self._create_organizational_relationships(repo_data['id'], organisation_id)
            
            logger.info(f"Comprehensively synced repository {repo_data['full_name']}: {items_synced} items")
            return items_synced
            
        except Exception as e:
            logger.error(f"Failed to sync repository comprehensively {repo_data.get('full_name', 'unknown')}: {str(e)}")
            return 0

    def _create_organization_member_relationship(self, member: Dict, org_data: Dict, organisation_id: str):
        """Create relationship between organization and member."""
        try:
            params = {
                'login': member.get('login'),
                'github_org_id': org_data['id'],
                'role': member.get('org_role', 'member'),
                'join_date': datetime.now().isoformat()
            }
            execute_write_query(self.organization_queries.ADD_MEMBER_TO_ORGANIZATION, params)
            
        except Exception as e:
            logger.error(f"Failed to create organization member relationship: {str(e)}")

    def _create_or_update_team(self, team_data: Dict, organisation_id: str, organization_id: int):
        """Create or update GitHub team."""
        try:
            params = {
                'team_id': team_data['id'],
                'organisation_id': organisation_id,
                'organization_id': organization_id,
                'name': team_data.get('name', ''),
                'slug': team_data.get('slug', ''),
                'description': team_data.get('description', ''),
                'privacy': team_data.get('privacy', 'closed'),
                'permission': team_data.get('permission', 'pull'),
                'html_url': team_data.get('html_url', ''),
                'created_at': team_data.get('created_at', datetime.now().isoformat()),
                'updated_at': team_data.get('updated_at', datetime.now().isoformat())
            }
            execute_write_query(self.team_queries.CREATE_OR_UPDATE_TEAM, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update team: {str(e)}")

    def _create_team_member_relationship(self, member: Dict, team: Dict, organisation_id: str):
        """Create relationship between team and member."""
        try:
            params = {
                'team_id': team['id'],
                'login': member.get('login'),
                'role': 'member',
                'joined_at': datetime.now().isoformat()
            }
            execute_write_query(self.team_queries.ADD_TEAM_MEMBER, params)
            
        except Exception as e:
            logger.error(f"Failed to create team member relationship: {str(e)}")

    def _create_team_repository_relationship(self, team: Dict, repo: Dict, organisation_id: str):
        """Create relationship between team and repository."""
        try:
            params = {
                'team_id': team['id'],
                'repository_id': repo['id'],
                'permission': 'read',
                'granted_at': datetime.now().isoformat()
            }
            execute_write_query(self.team_queries.ADD_TEAM_REPOSITORY_ACCESS, params)
            
        except Exception as e:
            logger.error(f"Failed to create team repository relationship: {str(e)}")

    def _create_repository_collaborator_relationship(self, collaborator: Dict, repo_data: Dict, organisation_id: str):
        """Create relationship between repository and collaborator."""
        try:
            params = {
                'repository_id': repo_data['id'],
                'login': collaborator.get('login'),
                'role': collaborator.get('permissions', {}).get('admin', False) and 'admin' or 'write',
                'granted_at': datetime.now().isoformat()
            }
            execute_write_query(self.repository_queries.ADD_REPOSITORY_COLLABORATOR, params)
            
        except Exception as e:
            logger.error(f"Failed to create repository collaborator relationship: {str(e)}")

    def _create_or_update_branch(self, branch_data: Dict, repository_id: int, organisation_id: str):
        """Create or update GitHub branch."""
        try:
            params = {
                'name': branch_data['name'],
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'last_commit_sha': branch_data.get('commit', {}).get('sha', ''),
                'protected': branch_data.get('protection') is not None,
                'default': branch_data.get('name') == 'main' or branch_data.get('name') == 'master',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # Create branch using a direct query (we'll need to add this to queries)
            branch_query = """
            MERGE (branch:GitHubBranch {name: $name, repository_id: $repository_id})
            SET branch.organisation_id = $organisation_id,
                branch.last_commit_sha = $last_commit_sha,
                branch.protected = $protected,
                branch.default = $default,
                branch.created_at = $created_at,
                branch.updated_at = $updated_at
            WITH branch
            MATCH (repo:GitHubRepository {id: $repository_id})
            MERGE (branch)-[:BELONGS_TO]->(repo)
            RETURN branch
            """
            
            execute_write_query(branch_query, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update branch: {str(e)}")

    def _create_or_update_tag(self, tag_data: Dict, repository_id: int, organisation_id: str):
        """Create or update GitHub tag."""
        try:
            params = {
                'name': tag_data['name'],
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'sha': tag_data.get('commit', {}).get('sha', ''),
                'commit_sha': tag_data.get('commit', {}).get('sha', ''),
                'message': tag_data.get('message', ''),
                'tagger_name': tag_data.get('tagger', {}).get('name', ''),
                'tagger_email': tag_data.get('tagger', {}).get('email', ''),
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.tag_queries.CREATE_OR_UPDATE_TAG, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update tag: {str(e)}")

    def _create_or_update_release(self, release_data: Dict, repository_id: int, organisation_id: str):
        """Create or update GitHub release."""
        try:
            params = {
                'id': release_data['id'],
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'tag_name': release_data.get('tag_name', ''),
                'name': release_data.get('name', ''),
                'body': release_data.get('body', ''),
                'draft': release_data.get('draft', False),
                'prerelease': release_data.get('prerelease', False),
                'html_url': release_data.get('html_url', ''),
                'created_at': release_data.get('created_at', datetime.now().isoformat()),
                'published_at': release_data.get('published_at')
            }
            execute_write_query(self.release_queries.CREATE_OR_UPDATE_RELEASE, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update release: {str(e)}")

    def _create_or_update_workflow(self, workflow_data: Dict, repository_id: int, organisation_id: str):
        """Create or update GitHub workflow."""
        try:
            # Create workflow using a direct query (we'll need to add this to queries)
            workflow_query = """
            MERGE (workflow:GitHubWorkflow {id: $id})
            SET workflow.repository_id = $repository_id,
                workflow.organisation_id = $organisation_id,
                workflow.name = $name,
                workflow.path = $path,
                workflow.state = $state,
                workflow.html_url = $html_url,
                workflow.created_at = $created_at,
                workflow.updated_at = $updated_at
            WITH workflow
            MATCH (repo:GitHubRepository {id: $repository_id})
            MERGE (workflow)-[:BELONGS_TO]->(repo)
            RETURN workflow
            """
            
            params = {
                'id': workflow_data['id'],
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'name': workflow_data.get('name', ''),
                'path': workflow_data.get('path', ''),
                'state': workflow_data.get('state', 'active'),
                'html_url': workflow_data.get('html_url', ''),
                'created_at': workflow_data.get('created_at', datetime.now().isoformat()),
                'updated_at': workflow_data.get('updated_at', datetime.now().isoformat())
            }
            
            execute_write_query(workflow_query, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update workflow: {str(e)}")

    def _create_or_update_comment(self, comment_data: Dict, organisation_id: str,
                                issue_id: int = None, pull_request_id: int = None, commit_sha: str = None):
        """Create or update GitHub comment."""
        try:
            params = {
                'id': comment_data['id'],
                'organisation_id': organisation_id,
                'body': comment_data.get('body', ''),
                'html_url': comment_data.get('html_url', ''),
                'created_at': comment_data.get('created_at', datetime.now().isoformat()),
                'updated_at': comment_data.get('updated_at', datetime.now().isoformat())
            }
            
            if issue_id:
                params['issue_id'] = issue_id
            elif pull_request_id:
                params['pull_request_id'] = pull_request_id
            elif commit_sha:
                params['commit_sha'] = commit_sha
            
            execute_write_query(self.comment_queries.CREATE_OR_UPDATE_COMMENT, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update comment: {str(e)}")

    def _create_or_update_review(self, review_data: Dict, organisation_id: str, pull_request_id: int):
        """Create or update GitHub review."""
        try:
            params = {
                'id': review_data['id'],
                'organisation_id': organisation_id,
                'pull_request_id': pull_request_id,
                'state': review_data.get('state', 'commented'),
                'body': review_data.get('body', ''),
                'html_url': review_data.get('html_url', ''),
                'submitted_at': review_data.get('submitted_at', datetime.now().isoformat())
            }
            execute_write_query(self.review_queries.CREATE_OR_UPDATE_REVIEW, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update review: {str(e)}")

    def _create_or_update_review_comment(self, review_comment_data: Dict, organisation_id: str, pull_request_id: int):
        """Create or update GitHub review comment."""
        try:
            # Create review comment using a direct query
            review_comment_query = """
            MERGE (comment:GitHubReviewComment {id: $id})
            SET comment.organisation_id = $organisation_id,
                comment.pull_request_id = $pull_request_id,
                comment.review_id = $review_id,
                comment.path = $path,
                comment.position = $position,
                comment.line = $line,
                comment.body = $body,
                comment.html_url = $html_url,
                comment.created_at = $created_at,
                comment.updated_at = $updated_at
            WITH comment
            MATCH (pr:GitHubPullRequest {id: $pull_request_id})
            MERGE (comment)-[:BELONGS_TO]->(pr)
            RETURN comment
            """
            
            params = {
                'id': review_comment_data['id'],
                'organisation_id': organisation_id,
                'pull_request_id': pull_request_id,
                'review_id': review_comment_data.get('pull_request_review_id'),
                'path': review_comment_data.get('path', ''),
                'position': review_comment_data.get('position'),
                'line': review_comment_data.get('line'),
                'body': review_comment_data.get('body', ''),
                'html_url': review_comment_data.get('html_url', ''),
                'created_at': review_comment_data.get('created_at', datetime.now().isoformat()),
                'updated_at': review_comment_data.get('updated_at', datetime.now().isoformat())
            }
            
            execute_write_query(review_comment_query, params)
            
        except Exception as e:
            logger.error(f"Failed to create/update review comment: {str(e)}")

    def map_github_entities_to_departments(self, organisation_id: str):
        """
        Map GitHub entities to departments following GDrive's department access patterns.
        This mirrors how GDrive maps folders and files to departments.
        """
        try:
            logger.info(f"Mapping GitHub entities to departments for organisation: {organisation_id}")

            params = {
                'organisation_id': organisation_id,
                'granted_at': datetime.now().isoformat()
            }

            # Use the comprehensive department mapping query
            result = execute_write_query(
                self.relationship_queries.CREATE_GITHUB_USER_DEPARTMENT_ACCESS,
                params
            )

            if result and len(result) > 0:
                repo_mappings = result[0].get('repo_mappings', 0)
                issue_mappings = result[0].get('issue_mappings', 0)
                pr_mappings = result[0].get('pr_mappings', 0)

                logger.info(f"Successfully mapped GitHub entities to departments: "
                          f"{repo_mappings} repositories, {issue_mappings} issues, {pr_mappings} pull requests")
            else:
                logger.info("No GitHub entities found to map to departments")

        except Exception as e:
            logger.error(f"Error mapping GitHub entities to departments: {str(e)}")

    def map_github_users_to_org_users(self, organisation_id: str):
        """
        Map GitHub users to organizational users following GDrive's user mapping patterns.
        This creates relationships between GitHubUser nodes and organizational User nodes.
        """
        try:
            logger.info(f"Mapping GitHub users to organizational users for organisation: {organisation_id}")

            # Get all GitHub users for this organization
            github_users_query = """
            MATCH (gu:GitHubUser {organisation_id: $organisation_id})
            WHERE gu.email IS NOT NULL
            RETURN gu.id as github_user_id, gu.login as github_login, gu.email as email
            """

            github_users = execute_read_query(github_users_query, {'organisation_id': organisation_id})

            mapped_count = 0
            for github_user in github_users:
                try:
                    # Try to map to existing organizational user by email
                    mapping_params = {
                        'email': github_user['email'],
                        'github_login': github_user['github_login'],
                        'organisation_id': organisation_id,
                        'mapped_at': datetime.now().isoformat()
                    }

                    result = execute_write_query(
                        self.relationship_queries.CREATE_USER_GITHUB_USER_MAPPING,
                        mapping_params
                    )

                    if result:
                        mapped_count += 1

                except Exception as e:
                    logger.warning(f"Failed to map GitHub user {github_user['github_login']}: {str(e)}")
                    continue

            logger.info(f"Successfully mapped {mapped_count} GitHub users to organizational users")

        except Exception as e:
            logger.error(f"Error mapping GitHub users to organizational users: {str(e)}")

    def sync_github_incremental(self, organisation_id: str, since_date: str = None) -> Tuple[bool, str, int, int]:
        """
        Perform incremental sync of GitHub data following GDrive's incremental sync patterns.

        Args:
            organisation_id: The organization ID
            since_date: ISO date string for incremental sync (if None, uses last sync time)

        Returns:
            Tuple containing success status, message, repositories synced, and total items synced
        """
        try:
            logger.info(f"Starting GitHub incremental sync for organisation: {organisation_id}")

            # Get last sync time if since_date not provided (mirroring GDrive pattern)
            if not since_date:
                since_date = self._get_last_sync_time(organisation_id)
                if not since_date:
                    logger.info("No previous sync found, performing full sync")
                    return self.sync_github(organisation_id, full_sync=True)

            # Get GitHub credentials
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", 0, 0

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", 0, 0

            # Create robust session with retry strategy
            session = self._create_robust_session(token)
            
            # Test authentication
            auth_test = self._safe_api_call(session, "https://api.github.com/user", context="authentication test")
            if not auth_test:
                logger.error("GitHub authentication failed")
                return False, "GitHub authentication failed", 0, 0
            
            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            repositories_synced = 0
            total_items_synced = 0

            # Fetch repositories updated since last sync
            repositories = self._fetch_repositories_since(session, since_date)

            for repo_data in repositories:
                try:
                    # Check if repository was updated since last sync
                    if self._is_repository_updated_since(repo_data, since_date):
                        self._create_or_update_repository(repo_data, organisation_id)
                        repositories_synced += 1
                        total_items_synced += 1

                        # Sync recent activity (issues, PRs, commits) for updated repositories
                        recent_items = self._sync_repository_recent_activity(
                            session, repo_data, organisation_id, since_date
                        )
                        total_items_synced += recent_items

                except Exception as e:
                    logger.error(f"Error in incremental sync for repository {repo_data.get('full_name', 'unknown')}: {str(e)}")
                    continue

            # Update last sync time
            self._update_last_sync_time(organisation_id)

            # Sync organizational relationships for any new/updated entities
            self.sync_organizational_access_patterns(organisation_id)

            logger.info(f"GitHub incremental sync completed: {repositories_synced} repositories, {total_items_synced} total items")
            return True, f"Successfully synced {repositories_synced} updated repositories", repositories_synced, total_items_synced

        except Exception as e:
            logger.error(f"Error during GitHub incremental sync: {str(e)}")
            return False, f"Incremental sync failed: {str(e)}", 0, 0

    def _get_last_sync_time(self, organisation_id: str) -> Optional[str]:
        """Get the last sync time for the organisation (mirroring GDrive pattern)."""
        try:
            query = """
            MATCH (o:Organisation {id: $organisation_id})-[:HAS_SOURCE]->(s:Source {type: 'github'})
            RETURN s.last_sync_time as last_sync_time
            """

            result = execute_read_query(query, {'organisation_id': organisation_id})
            if result and result[0].get('last_sync_time'):
                return result[0]['last_sync_time']
            return None

        except Exception as e:
            logger.error(f"Error getting last sync time: {str(e)}")
            return None

    def _fetch_repositories_since(self, session: requests.Session, since_date: str) -> List[Dict]:
        """Fetch repositories updated since the given date."""
        try:
            url = "https://api.github.com/user/repos"
            params = {
                'type': 'all',
                'sort': 'updated',
                'since': since_date,
                'per_page': 100
            }
            
            response_data = self._safe_api_call(session, url, params, context=f"repositories since {since_date}")
            if response_data:
                return response_data if isinstance(response_data, list) else [response_data]
            else:
                logger.warning(f"No repositories found since {since_date}")
                return []
                
        except Exception as e:
            logger.error(f"Failed to fetch repositories since {since_date}: {str(e)}")
            return []

    def _is_repository_updated_since(self, repo_data: Dict, since_date: str) -> bool:
        """Check if repository was updated since the given date."""
        try:
            from datetime import datetime
            repo_updated = datetime.fromisoformat(repo_data.get('updated_at', '').replace('Z', '+00:00'))
            since_datetime = datetime.fromisoformat(since_date.replace('Z', '+00:00'))
            return repo_updated > since_datetime
        except Exception as e:
            logger.warning(f"Error comparing dates for repository update check: {str(e)}")
            return True  # Default to sync if we can't determine

    def _sync_repository_recent_activity(self, session: requests.Session, repo_data: Dict,
                                       organisation_id: str, since_date: str) -> int:
        """Sync recent activity for a repository (issues, PRs, commits) since given date."""
        items_synced = 0

        try:
            owner = repo_data['owner']['login']
            repo_name = repo_data['name']

            # Sync recent issues
            recent_issues = self._fetch_repository_issues_since(session, owner, repo_name, since_date)
            for issue in recent_issues:
                self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                items_synced += 1

            # Sync recent pull requests
            recent_prs = self._fetch_repository_pull_requests_since(session, owner, repo_name, since_date)
            for pr in recent_prs:
                self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                items_synced += 1

            # Sync recent commits
            recent_commits = self._fetch_repository_commits_since(session, owner, repo_name, since_date)
            for commit in recent_commits:
                self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                items_synced += 1

        except Exception as e:
            logger.error(f"Error syncing recent activity for repository {repo_data.get('full_name', 'unknown')}: {str(e)}")

        return items_synced

    def _update_last_sync_time(self, organisation_id: str):
        """Update the last sync time for the organisation (following GDrive's Source pattern)."""
        try:
            current_time = datetime.now().isoformat()
            params = {
                'organisation_id': organisation_id,
                'sync_time': current_time
            }
            execute_write_query(self.sync_queries.UPDATE_LAST_SYNC_TIME, params)
            logger.debug(f"Updated last sync time for organisation {organisation_id}: {current_time}")
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")

    def _fetch_repository_issues_since(self, session: requests.Session, owner: str, repo: str, since_date: str) -> List[Dict]:
        """Fetch repository issues updated since the given date."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/issues"
            params = {
                'state': 'all',
                'since': since_date,
                'per_page': 100
            }
            response = session.get(url, params=params)
            response.raise_for_status()
            # Filter out pull requests (GitHub API includes PRs in issues endpoint)
            return [issue for issue in response.json() if 'pull_request' not in issue]
        except Exception as e:
            logger.error(f"Failed to fetch issues since {since_date} for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_pull_requests_since(self, session: requests.Session, owner: str, repo: str, since_date: str) -> List[Dict]:
        """Fetch repository pull requests updated since the given date."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
            params = {
                'state': 'all',
                'sort': 'updated',
                'direction': 'desc',
                'per_page': 100
            }
            response = session.get(url, params=params)
            response.raise_for_status()

            # Filter by date since GitHub API doesn't support 'since' parameter for PRs
            from datetime import datetime
            since_datetime = datetime.fromisoformat(since_date.replace('Z', '+00:00'))

            filtered_prs = []
            for pr in response.json():
                try:
                    pr_updated = datetime.fromisoformat(pr.get('updated_at', '').replace('Z', '+00:00'))
                    if pr_updated > since_datetime:
                        filtered_prs.append(pr)
                except Exception:
                    continue

            return filtered_prs
        except Exception as e:
            logger.error(f"Failed to fetch pull requests since {since_date} for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_commits_since(self, session: requests.Session, owner: str, repo: str, since_date: str) -> List[Dict]:
        """Fetch repository commits since the given date."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {
                'since': since_date,
                'per_page': 50  # Limit commits to avoid rate limiting
            }
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch commits since {since_date} for {owner}/{repo}: {str(e)}")
            return []

    def get_sync_statistics(self, organisation_id: str) -> Dict[str, Any]:
        """Get sync statistics for an organisation."""
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for getting sync statistics")
                return {}

            params = {'organisation_id': organisation_id}
            result = execute_read_query(self.sync_queries.GET_SYNC_STATISTICS, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting sync statistics for organisation {organisation_id}: {str(e)}")
            return {}

    def search_github_content(self, organisation_id: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search GitHub content using hybrid search.

        Args:
            organisation_id: Organisation ID
            query: Search query
            limit: Maximum number of results

        Returns:
            List of search results
        """
        try:
            # Validate required parameters
            if not organisation_id:
                logger.error("organisation_id is required for searching GitHub content")
                return []

            if not query or not query.strip():
                logger.error("query is required for searching GitHub content")
                return []

            if limit <= 0:
                logger.warning("Invalid limit provided, using default limit of 10")
                limit = 10

            # Use hybrid search engine for GitHub content
            search_engine = HybridSearchEngine()

            # Define GitHub entity types for search
            entity_types = [
                'GitHubRepository',
                'GitHubIssue',
                'GitHubPullRequest',
                'GitHubCodeFile'
            ]

            results = search_engine.search(
                query=query,
                organisation_id=organisation_id,
                entity_types=entity_types,
                limit=limit
            )

            return results

        except Exception as e:
            logger.error(f"Error searching GitHub content for organisation {organisation_id}: {str(e)}")
            return []

    def get_repository_statistics(self, organisation_id: str, repository_id: int) -> Dict[str, Any]:
        """Get statistics for a specific repository."""
        try:
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id
            }
            result = execute_read_query(self.metadata_queries.GET_REPOSITORY_STATISTICS, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting repository statistics: {str(e)}")
            return {}

    def get_user_activity_summary(self, organisation_id: str, user_id: int) -> Dict[str, Any]:
        """Get activity summary for a GitHub user."""
        try:
            params = {
                'organisation_id': organisation_id,
                'user_id': user_id
            }
            result = execute_read_query(self.metadata_queries.GET_USER_ACTIVITY_SUMMARY, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting user activity summary: {str(e)}")
            return {}

    def fetch_top_level_repositories(self, organisation_id: str) -> Tuple[bool, str, List[Dict[str, str]]]:
        """
        Fetch top-level repositories from GitHub using credentials.
        """
        try:
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", []

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", []

            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })

            # Get user's repositories
            response = session.get('https://api.github.com/user/repos?affiliation=owner,collaborator&sort=updated')
            response.raise_for_status()
            repos = response.json()

            repo_list = []
            for repo in repos:
                # Create or update repository in Neo4j
                self._create_or_update_repository(repo, organisation_id)
                repo_list.append({
                    'id': str(repo['id']),
                    'name': repo['name'],
                    'full_name': repo['full_name'],
                    'html_url': repo['html_url'],
                    'description': repo.get('description', ''),
                    'private': repo.get('private', False),
                    'created_at': repo.get('created_at', ''),
                    'updated_at': repo.get('updated_at', ''),
                    'stargazers_count': repo.get('stargazers_count', 0),
                    'forks_count': repo.get('forks_count', 0)
                })

            return True, f"Found {len(repo_list)} repositories", repo_list

        except Exception as e:
            logger.error(f"Error fetching top-level repositories: {str(e)}")
            return False, f"Error fetching repositories: {str(e)}", []

    def disconnect_github(self, organisation_id: str) -> Tuple[bool, str]:
        """
        Disconnect GitHub for an organization (remove all synced data).
        """
        try:
            # Remove all GitHub repositories for this organization
            execute_write_query(self.repository_queries.DELETE_ORGANIZATION_REPOSITORIES,
                              {'organisation_id': organisation_id})

            # Remove all GitHub files for this organization
            execute_write_query(self.file_queries.DELETE_ORGANIZATION_FILES,
                              {'organisation_id': organisation_id})

            # Cancel scheduled syncs for this organization
            self._cancel_scheduled_syncs_for_organization(organisation_id)

            return True, "GitHub disconnected successfully"

        except Exception as e:
            logger.error(f"Error disconnecting GitHub: {str(e)}")
            return False, f"Error disconnecting GitHub: {str(e)}"

    def list_repository_files(self, user_id: str, repo_id: str, page: int = 1, page_size: int = 50) -> Tuple[List[Dict], int, int, int]:
        """
        List files in a GitHub repository with pagination.
        """
        try:
            skip = (page - 1) * page_size
            
            # Get files from repository
            files_result = execute_read_query(self.file_queries.LIST_FILES_IN_REPOSITORY, {
                'repository_id': int(repo_id),
                'skip': skip,
                'limit': page_size
            })

            # Get total count
            count_result = execute_read_query(self.file_queries.COUNT_FILES_IN_REPOSITORY, {
                'repository_id': int(repo_id)
            })
            
            total_count = count_result[0]['total_count'] if count_result else 0

            # Convert to list of dictionaries
            file_list = []
            for file_record in files_result:
                file_dict = dict(file_record)
                file_list.append({
                    'id': file_dict.get('id', ''),
                    'name': file_dict.get('name', ''),
                    'path': file_dict.get('path', ''),
                    'url': file_dict.get('html_url', ''),
                    'size': file_dict.get('size', 0),
                    'last_modified': file_dict.get('updated_at', ''),
                    'sha': file_dict.get('sha', ''),
                    'download_url': file_dict.get('download_url', '')
                })

            return file_list, total_count, page, page_size

        except Exception as e:
            logger.error(f"Error listing repository files: {str(e)}")
            return [], 0, page, page_size

    def get_repository_details(self, user_id: str, repo_id: str) -> Tuple[bool, str, Dict]:
        """
        Get detailed information about a repository.
        """
        try:
            # Query repository details
            result = execute_read_query(self.repository_queries.GET_REPOSITORY_DETAILS, {
                'repository_id': int(repo_id)
            })
            
            if not result:
                return False, "Repository not found", {}

            repo_data = dict(result[0])
            
            # Format the response similar to Google Drive's file details
            formatted_data = {
                'id': str(repo_data.get('id', '')),
                'name': repo_data.get('name', ''),
                'full_name': repo_data.get('full_name', ''),
                'url': repo_data.get('html_url', ''),
                'description': repo_data.get('description', ''),
                'stars': repo_data.get('stargazers_count', 0),
                'forks': repo_data.get('forks_count', 0),
                'issues': repo_data.get('open_issues_count', 0),
                'created_time': repo_data.get('created_at', ''),
                'updated_time': repo_data.get('updated_at', ''),
                'private': repo_data.get('private', False),
                'archived': repo_data.get('archived', False),
                'language': repo_data.get('language', ''),
                'default_branch': repo_data.get('default_branch', 'main')
            }

            return True, "Repository details retrieved successfully", formatted_data

        except Exception as e:
            logger.error(f"Error getting repository details: {str(e)}")
            return False, f"Error getting repository details: {str(e)}", {}

    def check_repository_access(self, user_id: str, repo_id: str) -> bool:
        """
        Check if a user has access to a repository.
        This method checks if the user has any relationship with the repository.
        """
        try:
            # Check if user has direct access to repository through GitHub user relationship
            result = execute_read_query(self.relationship_queries.CHECK_USER_REPOSITORY_ACCESS, {
                'user_id': user_id,
                'repository_id': int(repo_id)
            })
            
            return bool(result and result[0].get('has_access', False))

        except Exception as e:
            logger.error(f"Error checking repository access: {str(e)}")
            return False

    def batch_search_similar_documents(self, user_id: str, queries: List[str], top_k: int = 5) -> List[List[Dict]]:
        """
        Perform batch search for similar documents across GitHub content.
        """
        try:
            results = []
            
            # Get user's organization for search context
            user_org_result = execute_read_query(self.relationship_queries.GET_USER_ORGANISATION, {
                'user_id': user_id
            })
            
            if not user_org_result:
                logger.warning(f"No organization found for user {user_id}")
                return [[] for _ in queries]
            
            organisation_id = user_org_result[0]['organisation_id']
            
            # Perform search for each query
            for query in queries:
                search_results = self.search_github_content(
                    organisation_id=organisation_id,
                    query=query,
                    limit=top_k
                )
                results.append(search_results)

            return results

        except Exception as e:
            logger.error(f"Error in batch search similar documents: {str(e)}")
            return [[] for _ in queries]

    def _create_comprehensive_relationships(self, user_id: str, source_id: str,
                                          organisation_id: str, department_id: str = None) -> int:
        """
        Create comprehensive relationships between all GitHub entities based on the schema.
        
        Args:
            user_id: User ID
            source_id: Source ID
            organisation_id: Organisation ID
            department_id: Optional department ID
            
        Returns:
            Number of relationships created
        """
        relationships_created = 0
        
        try:
            logger.info("Creating comprehensive GitHub entity relationships")
            
            # 1. Repository ownership and contribution relationships
            relationships_created += self._create_repository_relationships(organisation_id)
            
            # 2. Issue and Pull Request relationships
            relationships_created += self._create_issue_pr_relationships(organisation_id)
            
            # 3. Commit relationships
            relationships_created += self._create_commit_relationships(organisation_id)
            
            # 4. Branch and Tag relationships
            relationships_created += self._create_branch_tag_relationships(organisation_id)
            
            # 5. File and Directory relationships
            relationships_created += self._create_file_directory_relationships(organisation_id)
            
            # 6. Organization and Team relationships
            relationships_created += self._create_organization_team_relationships(organisation_id)
            
            # 7. Comment and Social relationships
            relationships_created += self._create_comment_social_relationships(organisation_id)
            
            # 8. Release relationships
            relationships_created += self._create_release_relationships(organisation_id)
            
            # 9. Text chunk relationships
            relationships_created += self._create_text_chunk_relationships(organisation_id)
            
            logger.info(f"Created {relationships_created} comprehensive relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating comprehensive relationships: {str(e)}")
            return relationships_created

    def _create_repository_relationships(self, organisation_id: str) -> int:
        """Create repository-related relationships."""
        relationships_created = 0
        
        try:
            # OWNS_REPOSITORY relationships
            owns_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (r:GitHubRepository {organisation_id: $organisation_id})
            WHERE r.owner_login = u.login
            MERGE (u)-[rel:OWNS_REPOSITORY]->(r)
            SET rel.ownership_type = 'owner',
                rel.granted_at = COALESCE(r.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(owns_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # ORG_OWNS_REPO relationships
            org_owns_query = """
            MATCH (org:GitHubOrganization {organisation_id: $organisation_id})
            MATCH (r:GitHubRepository {organisation_id: $organisation_id})
            WHERE r.owner_login = org.login
            MERGE (org)-[rel:ORG_OWNS_REPO]->(r)
            SET rel.created_at = COALESCE(r.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(org_owns_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # CONTRIBUTES_TO relationships (based on commits)
            contributes_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (c:GitHubCommit {organisation_id: $organisation_id})
            MATCH (r:GitHubRepository {organisation_id: $organisation_id})
            WHERE (c.author_email = u.email OR c.author_name = u.name OR c.committer_email = u.email)
            AND c.repository_id = r.id
            WITH u, r, min(c.date) as first_contribution, max(c.date) as last_contribution
            MERGE (u)-[rel:CONTRIBUTES_TO]->(r)
            SET rel.first_contribution_at = first_contribution,
                rel.last_contribution_at = last_contribution,
                rel.role = 'contributor'
            RETURN count(rel) as count
            """
            
            result = execute_write_query(contributes_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # BELONGS_TO relationships for all repository entities
            belongs_to_query = """
            MATCH (r:GitHubRepository {organisation_id: $organisation_id})
            OPTIONAL MATCH (i:GitHubIssue {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (pr:GitHubPullRequest {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (c:GitHubCommit {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (f:GitHubCodeFile {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (d:GitHubDirectory {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (b:GitHubBranch {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (t:GitHubTag {organisation_id: $organisation_id, repository_id: r.id})
            OPTIONAL MATCH (rel:GitHubRelease {organisation_id: $organisation_id, repository_id: r.id})
            
            WITH r, collect(DISTINCT i) + collect(DISTINCT pr) + collect(DISTINCT c) + 
                 collect(DISTINCT f) + collect(DISTINCT d) + collect(DISTINCT b) + 
                 collect(DISTINCT t) + collect(DISTINCT rel) as entities
            
            UNWIND entities as entity
            WHERE entity IS NOT NULL
            MERGE (entity)-[belongs:BELONGS_TO]->(r)
            SET belongs.created_at = COALESCE(entity.created_at, datetime())
            RETURN count(belongs) as count
            """
            
            result = execute_write_query(belongs_to_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} repository relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating repository relationships: {str(e)}")
            return 0

    def _create_issue_pr_relationships(self, organisation_id: str) -> int:
        """Create issue and pull request relationships."""
        relationships_created = 0
        
        try:
            # CREATES_ISSUE relationships
            creates_issue_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (i:GitHubIssue {organisation_id: $organisation_id})
            WHERE i.user_login = u.login
            MERGE (u)-[rel:CREATES_ISSUE]->(i)
            SET rel.created_at = i.created_at
            RETURN count(rel) as count
            """
            
            result = execute_write_query(creates_issue_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # CREATES_PULL_REQUEST relationships
            creates_pr_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (pr:GitHubPullRequest {organisation_id: $organisation_id})
            WHERE pr.user_login = u.login
            MERGE (u)-[rel:CREATES_PULL_REQUEST]->(pr)
            SET rel.created_at = pr.created_at
            RETURN count(rel) as count
            """
            
            result = execute_write_query(creates_pr_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # ASSIGNED_TO relationships
            assigned_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (entity {organisation_id: $organisation_id})
            WHERE (entity:GitHubIssue OR entity:GitHubPullRequest)
            AND u.login IN COALESCE(entity.assignees, [])
            MERGE (u)-[rel:ASSIGNED_TO]->(entity)
            SET rel.assigned_at = COALESCE(entity.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(assigned_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # FIXES_ISSUE relationships (commits that reference issues)
            fixes_issue_query = """
            MATCH (c:GitHubCommit {organisation_id: $organisation_id})
            MATCH (i:GitHubIssue {organisation_id: $organisation_id})
            WHERE c.repository_id = i.repository_id
            AND (toLower(c.message) CONTAINS 'fixes #' + toString(i.number) 
                 OR toLower(c.message) CONTAINS 'closes #' + toString(i.number)
                 OR toLower(c.message) CONTAINS 'resolves #' + toString(i.number))
            MERGE (c)-[rel:FIXES_ISSUE]->(i)
            SET rel.fixed_at = c.date
            RETURN count(rel) as count
            """
            
            result = execute_write_query(fixes_issue_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} issue/PR relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating issue/PR relationships: {str(e)}")
            return 0

    def _create_commit_relationships(self, organisation_id: str) -> int:
        """Create commit-related relationships."""
        relationships_created = 0
        
        try:
            # AUTHORS_COMMIT relationships
            authors_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (c:GitHubCommit {organisation_id: $organisation_id})
            WHERE c.author_email = u.email OR c.author_name = u.name
            MERGE (u)-[rel:AUTHORS_COMMIT]->(c)
            SET rel.authored_at = c.date
            RETURN count(rel) as count
            """
            
            result = execute_write_query(authors_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # COMMITS_TO_BRANCH relationships
            commits_to_branch_query = """
            MATCH (c:GitHubCommit {organisation_id: $organisation_id})
            MATCH (b:GitHubBranch {organisation_id: $organisation_id})
            WHERE c.repository_id = b.repository_id
            AND (b.last_commit_sha = c.sha OR b.name = 'main' OR b.name = 'master' OR b.default = true)
            MERGE (c)-[rel:COMMITS_TO_BRANCH]->(b)
            SET rel.committed_at = c.date
            RETURN count(rel) as count
            """
            
            result = execute_write_query(commits_to_branch_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # COMMIT_IN_PR relationships
            commit_in_pr_query = """
            MATCH (c:GitHubCommit {organisation_id: $organisation_id})
            MATCH (pr:GitHubPullRequest {organisation_id: $organisation_id})
            WHERE c.repository_id = pr.repository_id
            AND c.date >= pr.created_at
            AND (pr.merged_at IS NULL OR c.date <= pr.merged_at)
            MERGE (c)-[rel:COMMIT_IN_PR]->(pr)
            SET rel.added_at = c.date
            RETURN count(rel) as count
            """
            
            result = execute_write_query(commit_in_pr_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} commit relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating commit relationships: {str(e)}")
            return 0

    def _create_branch_tag_relationships(self, organisation_id: str) -> int:
        """Create branch and tag relationships."""
        relationships_created = 0
        
        try:
            # CREATES_BRANCH relationships (inferred from first commit)
            creates_branch_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (b:GitHubBranch {organisation_id: $organisation_id})
            MATCH (c:GitHubCommit {organisation_id: $organisation_id})
            WHERE c.repository_id = b.repository_id
            AND (c.author_email = u.email OR c.author_name = u.name)
            WITH u, b, c ORDER BY c.date ASC
            WITH u, b, collect(c)[0] as first_commit
            MERGE (u)-[rel:CREATES_BRANCH]->(b)
            SET rel.created_at = COALESCE(b.created_at, first_commit.date)
            RETURN count(rel) as count
            """
            
            result = execute_write_query(creates_branch_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # TAGS_RELEASE relationships
            tags_release_query = """
            MATCH (t:GitHubTag {organisation_id: $organisation_id})
            MATCH (r:GitHubRelease {organisation_id: $organisation_id})
            WHERE t.repository_id = r.repository_id
            AND t.name = r.tag_name
            MERGE (t)-[rel:TAGS_RELEASE]->(r)
            SET rel.tagged_at = COALESCE(r.created_at, t.created_at)
            RETURN count(rel) as count
            """
            
            result = execute_write_query(tags_release_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} branch/tag relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating branch/tag relationships: {str(e)}")
            return 0

    def _create_file_directory_relationships(self, organisation_id: str) -> int:
        """Create file and directory relationships."""
        relationships_created = 0
        
        try:
            # CONTAINS_FILE relationships
            contains_file_query = """
            MATCH (d:GitHubDirectory {organisation_id: $organisation_id})
            MATCH (f:GitHubCodeFile {organisation_id: $organisation_id})
            WHERE f.repository_id = d.repository_id
            AND f.path STARTS WITH d.path + '/'
            AND NOT f.path CONTAINS '/' AFTER d.path + '/'
            MERGE (d)-[rel:CONTAINS_FILE]->(f)
            SET rel.added_at = COALESCE(f.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(contains_file_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # CONTAINS_DIRECTORY relationships
            contains_dir_query = """
            MATCH (parent:GitHubDirectory {organisation_id: $organisation_id})
            MATCH (child:GitHubDirectory {organisation_id: $organisation_id})
            WHERE parent.repository_id = child.repository_id
            AND child.path STARTS WITH parent.path + '/'
            AND NOT child.path CONTAINS '/' AFTER parent.path + '/'
            AND parent.path <> child.path
            MERGE (parent)-[rel:CONTAINS_DIRECTORY]->(child)
            SET rel.added_at = COALESCE(child.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(contains_dir_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} file/directory relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating file/directory relationships: {str(e)}")
            return 0

    def _create_organization_team_relationships(self, organisation_id: str) -> int:
        """Create organization and team relationships."""
        relationships_created = 0
        
        try:
            # MEMBER_OF_ORG relationships
            member_of_org_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (org:GitHubOrganization {organisation_id: $organisation_id})
            // Infer from repository ownership/contribution
            MATCH (u)-[:OWNS_REPOSITORY|CONTRIBUTES_TO]->(r:GitHubRepository)
            MATCH (org)-[:ORG_OWNS_REPO]->(r)
            MERGE (u)-[rel:MEMBER_OF_ORG]->(org)
            SET rel.role = 'member',
                rel.joined_at = COALESCE(u.created_at, datetime())
            RETURN count(DISTINCT rel) as count
            """
            
            result = execute_write_query(member_of_org_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # MEMBER_OF_TEAM relationships
            member_of_team_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (t:GitHubTeam {organisation_id: $organisation_id})
            WHERE u.login IN COALESCE(t.members, [])
            MERGE (u)-[rel:MEMBER_OF_TEAM]->(t)
            SET rel.role = 'member',
                rel.joined_at = COALESCE(t.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(member_of_team_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            # TEAM_HAS_ACCESS relationships
            team_access_query = """
            MATCH (t:GitHubTeam {organisation_id: $organisation_id})
            MATCH (r:GitHubRepository {organisation_id: $organisation_id})
            WHERE r.id IN COALESCE(t.repositories, [])
            MERGE (t)-[rel:TEAM_HAS_ACCESS]->(r)
            SET rel.permission = 'read',
                rel.granted_at = COALESCE(t.created_at, datetime())
            RETURN count(rel) as count
            """
            
            result = execute_write_query(team_access_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} organization/team relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating organization/team relationships: {str(e)}")
            return 0

    def _create_comment_social_relationships(self, organisation_id: str) -> int:
        """Create comment and social relationships."""
        relationships_created = 0
        
        try:
            # COMMENTS_ON relationships
            comments_on_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (c:GitHubComment {organisation_id: $organisation_id})
            MATCH (entity {organisation_id: $organisation_id})
            WHERE (entity:GitHubIssue OR entity:GitHubPullRequest)
            AND (c.issue_id = entity.id OR c.pull_request_id = entity.id)
            AND c.user_login = u.login
            MERGE (u)-[rel:COMMENTS_ON]->(entity)
            SET rel.commented_at = c.created_at
            RETURN count(rel) as count
            """
            
            result = execute_write_query(comments_on_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} comment/social relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating comment/social relationships: {str(e)}")
            return 0

    def _create_release_relationships(self, organisation_id: str) -> int:
        """Create release relationships."""
        relationships_created = 0
        
        try:
            # CREATES_RELEASE relationships
            creates_release_query = """
            MATCH (u:GitHubUser {organisation_id: $organisation_id})
            MATCH (r:GitHubRelease {organisation_id: $organisation_id})
            WHERE r.author_login = u.login
            MERGE (u)-[rel:CREATES_RELEASE]->(r)
            SET rel.created_at = r.created_at
            RETURN count(rel) as count
            """
            
            result = execute_write_query(creates_release_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} release relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating release relationships: {str(e)}")
            return 0

    def _create_text_chunk_relationships(self, organisation_id: str) -> int:
        """Create text chunk relationships."""
        relationships_created = 0
        
        try:
            # CHUNK_OF_FILE relationships
            chunk_of_file_query = """
            MATCH (chunk:GitHubTextChunk {organisation_id: $organisation_id})
            MATCH (f:GitHubCodeFile {organisation_id: $organisation_id})
            WHERE chunk.file_path = f.path
            AND chunk.repository_id = f.repository_id
            MERGE (chunk)-[rel:CHUNK_OF_FILE]->(f)
            SET rel.start_line = chunk.start_line,
                rel.end_line = chunk.end_line
            RETURN count(rel) as count
            """
            
            result = execute_write_query(chunk_of_file_query, {"organisation_id": organisation_id})
            if result and len(result) > 0:
                relationships_created += result[0].get('count', 0)
            
            logger.info(f"Created {relationships_created} text chunk relationships")
            return relationships_created
            
        except Exception as e:
            logger.error(f"Error creating text chunk relationships: {str(e)}")
            return 0
