# GitHub Connector Schema
version: 1.2
description: "Comprehensive schema for GitHub connector entities and relationships, aligned with constants."

nodes:
  GitHubRepository:
    description: "Represents a GitHub repository"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub repository ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      name:
        type: string
        description: "Repository name"
      full_name:
        type: string
        description: "Repository full name (owner/repo)"
      description:
        type: string
        description: "Repository description"
      private:
        type: boolean
        description: "Whether the repository is private"
      html_url:
        type: string
        description: "Repository HTML URL"
      default_branch:
        type: string
        description: "Default branch name"
      stargazers_count:
        type: integer
        description: "Number of stars"
      watchers_count:
        type: integer
        description: "Number of watchers"
      forks_count:
        type: integer
        description: "Number of forks"
      open_issues_count:
        type: integer
        description: "Number of open issues"
      archived:
        type: boolean
        description: "Whether the repository is archived"
      created_at:
        type: timestamp
        description: "Repository creation time"
      updated_at:
        type: timestamp
        description: "Repository last update time"

  GitHubUser:
    description: "Represents a GitHub user"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub user ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      login:
        type: string
        description: "User login name"
      name:
        type: string
        description: "User display name"
      email:
        type: string
        description: "User email address"
      html_url:
        type: string
        description: "User profile HTML URL"
      type:
        type: string
        description: "User type (User, Organization)"
      created_at:
        type: timestamp
        description: "User creation time"
      updated_at:
        type: timestamp
        description: "User last update time"

  GitHubOrganization:
    description: "Represents a GitHub organization"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub organization ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      login:
        type: string
        description: "Organization login name"
      name:
        type: string
        description: "Organization display name"
      description:
        type: string
        description: "Organization description"
      html_url:
        type: string
        description: "Organization HTML URL"
      public_repos:
        type: integer
        description: "Number of public repositories"
      public_gists:
        type: integer
        description: "Number of public gists"
      followers:
        type: integer
        description: "Number of followers"
      following:
        type: integer
        description: "Number of following"
      created_at:
        type: timestamp
        description: "Organization creation time"
      updated_at:
        type: timestamp
        description: "Organization last update time"

  GitHubCodeFile:
    description: "Represents a code file in GitHub"
    properties:
      path:
        type: string
        required: true
        description: "File path within the repository"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this file"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      sha:
        type: string
        description: "File content SHA hash"
      name:
        type: string
        description: "File name"
      content:
        type: string
        description: "File content"
      size:
        type: integer
        description: "File size in bytes"
      html_url:
        type: string
        description: "File HTML URL"
      download_url:
        type: string
        description: "File download URL"
      content_type:
        type: string
        description: "Content type (code, documentation, config, data)"
      vector_id:
        type: string
        description: "Vector ID for semantic search"
      vectorized_at:
        type: timestamp
        description: "When the file was vectorized"
      last_vectorized_modified_time:
        type: timestamp
        description: "Last modified time when vectorized"
      created_at:
        type: timestamp
        description: "File creation time"
      updated_at:
        type: timestamp
        description: "File last update time"

  GitHubIssue:
    description: "Represents a GitHub issue"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub issue ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this issue"
      number:
        type: integer
        description: "Issue number within the repository"
      title:
        type: string
        description: "Issue title"
      body:
        type: string
        description: "Issue body text"
      state:
        type: string
        description: "Issue state (open, closed)"
      html_url:
        type: string
        description: "Issue HTML URL"
      created_at:
        type: timestamp
        description: "Issue creation time"
      updated_at:
        type: timestamp
        description: "Issue last update time"
      closed_at:
        type: timestamp
        description: "Issue close time"

  GitHubPullRequest:
    description: "Represents a GitHub pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub pull request ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this pull request"
      number:
        type: integer
        description: "Pull request number within the repository"
      title:
        type: string
        description: "Pull request title"
      body:
        type: string
        description: "Pull request body text"
      state:
        type: string
        description: "Pull request state (open, closed, merged)"
      html_url:
        type: string
        description: "Pull request HTML URL"
      head_branch:
        type: string
        description: "Head branch name"
      base_branch:
        type: string
        description: "Base branch name"
      merged:
        type: boolean
        description: "Whether the pull request is merged"
      created_at:
        type: timestamp
        description: "Pull request creation time"
      updated_at:
        type: timestamp
        description: "Pull request last update time"
      merged_at:
        type: timestamp
        description: "Pull request merge time"

  GitHubCommit:
    description: "Represents a GitHub commit"
    properties:
      sha:
        type: string
        required: true
        description: "Commit SHA hash"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this commit"
      message:
        type: string
        description: "Commit message"
      author_name:
        type: string
        description: "Name of the commit author"
      author_email:
        type: string
        description: "Email of the commit author"
      committer_name:
        type: string
        description: "Name of the committer"
      committer_email:
        type: string
        description: "Email of the committer"
      html_url:
        type: string
        description: "Commit HTML URL"
      date:
        type: timestamp
        description: "Commit date"
      created_at:
        type: timestamp
        description: "Commit creation time"

  GitHubBranch:
    description: "Represents a repository branch"
    properties:
      name:
        type: string
        required: true
        description: "Branch name"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this branch"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      last_commit_sha:
        type: string
        description: "SHA of the last commit on this branch"
      protected:
        type: boolean
        description: "Whether the branch is protected"
      default:
        type: boolean
        description: "Whether this is the default branch"
      created_at:
        type: timestamp
        description: "Branch creation time"
      updated_at:
        type: timestamp
        description: "Branch last update time"

  GitHubDirectory:
    description: "Represents a directory in a GitHub repository"
    properties:
      path:
        type: string
        required: true
        description: "Directory path within the repository"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this directory"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      name:
        type: string
        description: "Directory name"
      sha:
        type: string
        description: "Directory SHA hash"
      html_url:
        type: string
        description: "Directory HTML URL"
      created_at:
        type: timestamp
        description: "Directory creation time"
      updated_at:
        type: timestamp
        description: "Directory last update time"

  GitHubTeam:
    description: "Represents a GitHub team"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub team ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      organization_id:
        type: integer
        required: true
        description: "GitHub organization ID that owns this team"
      name:
        type: string
        description: "Team name"
      slug:
        type: string
        description: "Team slug"
      description:
        type: string
        description: "Team description"
      privacy:
        type: string
        description: "Team privacy (closed, secret)"
      permission:
        type: string
        description: "Team permission level"
      html_url:
        type: string
        description: "Team HTML URL"
      created_at:
        type: timestamp
        description: "Team creation time"
      updated_at:
        type: timestamp
        description: "Team last update time"

  GitHubTag:
    description: "Represents a Git tag"
    properties:
      name:
        type: string
        required: true
        description: "Tag name"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this tag"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      sha:
        type: string
        description: "Tag SHA hash"
      commit_sha:
        type: string
        description: "SHA of the commit this tag points to"
      message:
        type: string
        description: "Tag message"
      tagger_name:
        type: string
        description: "Name of the tagger"
      tagger_email:
        type: string
        description: "Email of the tagger"
      created_at:
        type: timestamp
        description: "Tag creation time"

  GitHubRelease:
    description: "Represents a GitHub release"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub release ID"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this release"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      tag_name:
        type: string
        description: "Tag name associated with this release"
      name:
        type: string
        description: "Release name"
      body:
        type: string
        description: "Release body text"
      draft:
        type: boolean
        description: "Whether the release is a draft"
      prerelease:
        type: boolean
        description: "Whether the release is a prerelease"
      html_url:
        type: string
        description: "Release HTML URL"
      created_at:
        type: timestamp
        description: "Release creation time"
      published_at:
        type: timestamp
        description: "Release publication time"

  GitHubComment:
    description: "Represents a comment on an issue or pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub comment ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      issue_id:
        type: integer
        description: "ID of the issue this comment belongs to"
      pull_request_id:
        type: integer
        description: "ID of the pull request this comment belongs to"
      body:
        type: string
        description: "Comment body text"
      html_url:
        type: string
        description: "Comment HTML URL"
      created_at:
        type: timestamp
        description: "Comment creation time"
      updated_at:
        type: timestamp
        description: "Comment last update time"

  GitHubReview:
    description: "Represents a review on a pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub review ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      pull_request_id:
        type: integer
        required: true
        description: "ID of the pull request this review belongs to"
      state:
        type: string
        description: "Review state (approved, changes_requested, commented)"
      body:
        type: string
        description: "Review body text"
      html_url:
        type: string
        description: "Review HTML URL"
      submitted_at:
        type: timestamp
        description: "Review submission time"

  GitHubMergeCommit:
    description: "Represents a merge commit"
    properties:
      sha:
        type: string
        required: true
        description: "Merge commit SHA hash"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this merge commit"
      pull_request_id:
        type: integer
        description: "ID of the pull request that was merged"
      message:
        type: string
        description: "Merge commit message"
      created_at:
        type: timestamp
        description: "Merge commit creation time"

  GitHubReviewComment:
    description: "Represents a review comment on a pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub review comment ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      pull_request_id:
        type: integer
        required: true
        description: "ID of the pull request this review comment belongs to"
      review_id:
        type: integer
        description: "ID of the review this comment belongs to"
      path:
        type: string
        description: "File path the comment is on"
      position:
        type: integer
        description: "Position in the diff"
      line:
        type: integer
        description: "Line number in the file"
      body:
        type: string
        description: "Review comment body text"
      html_url:
        type: string
        description: "Review comment HTML URL"
      created_at:
        type: timestamp
        description: "Review comment creation time"
      updated_at:
        type: timestamp
        description: "Review comment last update time"

  GitHubTextChunk:
    description: "Represents a chunk of text from a GitHub file for semantic search"
    properties:
      id:
        type: string
        required: true
        description: "Unique chunk ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      file_path:
        type: string
        required: true
        description: "Path of the file this chunk belongs to"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing the file"
      content:
        type: string
        description: "Chunk content"
      start_line:
        type: integer
        description: "Starting line number"
      end_line:
        type: integer
        description: "Ending line number"
      vector_id:
        type: string
        description: "Vector ID for semantic search"
      created_at:
        type: timestamp
        description: "Chunk creation time"
      updated_at:
        type: timestamp
        description: "Chunk last update time"

relationships:
  # Repository relationships
  OWNS_REPOSITORY:
    from: GitHubUser
    to: GitHubRepository
    description: "User owns a repository"
    direction: "->"
    properties:
      ownership_type:
        type: string
        description: "Type of ownership (owner, admin)"
      granted_at:
        type: timestamp
        description: "When ownership was granted"

  CONTRIBUTES_TO:
    from: GitHubUser
    to: GitHubRepository
    description: "User contributes to a repository"
    direction: "->"
    properties:
      first_contribution_at:
        type: timestamp
        description: "When user first contributed"
      last_contribution_at:
        type: timestamp
        description: "When user last contributed"
      role:
        type: string
        description: "Contributor role (read, write, admin)"

  FORKS_FROM:
    from: GitHubRepository
    to: GitHubRepository
    description: "A repository is forked from another"
    direction: "->"
    properties:
      forked_at:
        type: timestamp
        description: "When the fork was created"

  STARS:
    from: GitHubUser
    to: GitHubRepository
    description: "User stars a repository"
    direction: "->"
    properties:
      starred_at:
        type: timestamp
        description: "When the repository was starred"

  WATCHES:
    from: GitHubUser
    to: GitHubRepository
    description: "User watches a repository"
    direction: "->"
    properties:
      watched_at:
        type: timestamp
        description: "When the repository was watched"

  ORG_OWNS_REPO:
    from: GitHubOrganization
    to: GitHubRepository
    description: "Organization owns a repository"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the repository was created"

  # Issue relationships
  CREATES_ISSUE:
    from: GitHubUser
    to: GitHubIssue
    description: "User creates an issue"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the issue was created"

  ASSIGNED_TO_ISSUE:
    from: GitHubUser
    to: GitHubIssue
    description: "User is assigned to an issue"
    direction: "->"
    properties:
      assigned_at:
        type: timestamp
        description: "When the user was assigned to the issue"

  CLOSES_ISSUE:
    from: GitHubUser
    to: GitHubIssue
    description: "User closes an issue"
    direction: "->"
    properties:
      closed_at:
        type: timestamp
        description: "When the issue was closed"

  FIXES_ISSUE:
    from: GitHubCommit
    to: GitHubIssue
    description: "Commit fixes an issue"
    direction: "->"
    properties:
      fixed_at:
        type: timestamp
        description: "When the issue was fixed"

  # Pull Request relationships
  CREATES_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User creates a pull request"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the pull request was created"

  REVIEWS_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User reviews a pull request"
    direction: "->"
    properties:
      reviewed_at:
        type: timestamp
        description: "When the pull request was reviewed"

  ASSIGNED_TO_PR:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User is assigned to a pull request"
    direction: "->"
    properties:
      assigned_at:
        type: timestamp
        description: "When the user was assigned to the pull request"

  MERGES_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User merges a pull request"
    direction: "->"
    properties:
      merged_at:
        type: timestamp
        description: "When the pull request was merged"

  APPROVES_PR:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User approves a pull request"
    direction: "->"
    properties:
      approved_at:
        type: timestamp
        description: "When the pull request was approved"

  REQUESTS_CHANGES_PR:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User requests changes on a pull request"
    direction: "->"
    properties:
      requested_at:
        type: timestamp
        description: "When changes were requested"

  COMMIT_IN_PR:
    from: GitHubCommit
    to: GitHubPullRequest
    description: "Commit is part of a pull request"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When the commit was added to the pull request"

  # Commit relationships
  AUTHORS_COMMIT:
    from: GitHubUser
    to: GitHubCommit
    description: "User authors a commit"
    direction: "->"
    properties:
      authored_at:
        type: timestamp
        description: "When the commit was authored"

  COMMITS_TO_BRANCH:
    from: GitHubCommit
    to: GitHubBranch
    description: "A commit is made to a branch"
    direction: "->"
    properties:
      committed_at:
        type: timestamp
        description: "When the commit was made to the branch"

  PARENT_COMMIT:
    from: GitHubCommit
    to: GitHubCommit
    description: "Commit has a parent commit"
    direction: "->"
    properties:
      relationship_type:
        type: string
        description: "Type of parent relationship"

  CHILD_OF_COMMIT:
    from: GitHubCommit
    to: GitHubCommit
    description: "Commit is a child of another commit"
    direction: "->"
    properties:
      relationship_type:
        type: string
        description: "Type of child relationship"

  # Branch relationships
  CREATES_BRANCH:
    from: GitHubUser
    to: GitHubBranch
    description: "User creates a branch"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the branch was created"

  MERGES_BRANCH:
    from: GitHubUser
    to: GitHubBranch
    description: "User merges a branch"
    direction: "->"
    properties:
      merged_at:
        type: timestamp
        description: "When the branch was merged"

  BRANCH_FROM:
    from: GitHubBranch
    to: GitHubBranch
    description: "Branch is created from another branch"
    direction: "->"
    properties:
      branched_at:
        type: timestamp
        description: "When the branch was created"

  PROTECTS_BRANCH:
    from: GitHubUser
    to: GitHubBranch
    description: "User protects a branch"
    direction: "->"
    properties:
      protected_at:
        type: timestamp
        description: "When the branch was protected"

  # File and Directory relationships
  MODIFIES_FILE:
    from: GitHubCommit
    to: GitHubCodeFile
    description: "A commit modifies a file"
    direction: "->"
    properties:
      modification_type:
        type: string
        description: "Type of modification (added, modified, deleted)"
      lines_added:
        type: integer
        description: "Number of lines added"
      lines_removed:
        type: integer
        description: "Number of lines removed"

  CREATES_FILE:
    from: GitHubCommit
    to: GitHubCodeFile
    description: "A commit creates a file"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the file was created"

  DELETES_FILE:
    from: GitHubCommit
    to: GitHubCodeFile
    description: "A commit deletes a file"
    direction: "->"
    properties:
      deleted_at:
        type: timestamp
        description: "When the file was deleted"

  CONTAINS_FILE:
    from: GitHubDirectory
    to: GitHubCodeFile
    description: "A directory contains a file"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When the file was added to the directory"

  CONTAINS_DIRECTORY:
    from: GitHubDirectory
    to: GitHubDirectory
    description: "A directory contains another directory"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When the subdirectory was added"

  PARENT_DIRECTORY:
    from: GitHubDirectory
    to: GitHubDirectory
    description: "Directory has a parent directory"
    direction: "->"
    properties:
      relationship_type:
        type: string
        description: "Type of parent relationship"

  BELONGS_TO:
    from: [GitHubIssue, GitHubPullRequest, GitHubCommit, GitHubCodeFile, GitHubDirectory, GitHubBranch, GitHubTag, GitHubRelease]
    to: GitHubRepository
    description: "An entity belongs to a repository"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the entity was created in the repository"

  CHUNK_OF_FILE:
    from: GitHubTextChunk
    to: GitHubCodeFile
    description: "Text chunk belongs to a file"
    direction: "->"
    properties:
      start_line:
        type: integer
        description: "Starting line number"
      end_line:
        type: integer
        description: "Ending line number"

  # Release relationships
  CREATES_RELEASE:
    from: GitHubUser
    to: GitHubRelease
    description: "User creates a release"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the release was created"

  TAGS_RELEASE:
    from: GitHubTag
    to: GitHubRelease
    description: "Tag is associated with a release"
    direction: "->"
    properties:
      tagged_at:
        type: timestamp
        description: "When the tag was associated with the release"

  # Organization and Team relationships
  MEMBER_OF_ORG:
    from: GitHubUser
    to: GitHubOrganization
    description: "User is a member of an organization"
    direction: "->"
    properties:
      role:
        type: string
        description: "User's role in the organization (member, admin, owner)"
      joined_at:
        type: timestamp
        description: "When the user joined the organization"

  ADMIN_OF_ORG:
    from: GitHubUser
    to: GitHubOrganization
    description: "User is an admin of an organization"
    direction: "->"
    properties:
      granted_at:
        type: timestamp
        description: "When admin privileges were granted"

  MEMBER_OF_TEAM:
    from: GitHubUser
    to: GitHubTeam
    description: "User is a member of a team"
    direction: "->"
    properties:
      role:
        type: string
        description: "User's role in the team (member, maintainer)"
      joined_at:
        type: timestamp
        description: "When the user joined the team"

  ADMIN_OF_TEAM:
    from: GitHubUser
    to: GitHubTeam
    description: "User is an admin of a team"
    direction: "->"
    properties:
      granted_at:
        type: timestamp
        description: "When admin privileges were granted"

  TEAM_HAS_ACCESS:
    from: GitHubTeam
    to: GitHubRepository
    description: "A team has access to a repository"
    direction: "->"
    properties:
      permission:
        type: string
        description: "Permission level (read, write, admin)"
      granted_at:
        type: timestamp
        description: "When access was granted"

  # Comment and Social relationships
  COMMENTS_ON:
    from: GitHubUser
    to: [GitHubIssue, GitHubPullRequest]
    description: "User comments on an issue or pull request"
    direction: "->"
    properties:
      commented_at:
        type: timestamp
        description: "When the comment was made"

  REPLY_TO_COMMENT:
    from: GitHubComment
    to: GitHubComment
    description: "Comment is a reply to another comment"
    direction: "->"
    properties:
      replied_at:
        type: timestamp
        description: "When the reply was made"

  ASSIGNED_TO:
    from: GitHubUser
    to: [GitHubIssue, GitHubPullRequest]
    description: "User is assigned to an issue or pull request"
    direction: "->"
    properties:
      assigned_at:
        type: timestamp
        description: "When the assignment was made"

  MENTIONS:
    from: [GitHubIssue, GitHubPullRequest, GitHubComment]
    to: GitHubUser
    description: "Entity mentions a user"
    direction: "->"
    properties:
      mentioned_at:
        type: timestamp
        description: "When the mention was made"

  LINKS_TO:
    from: [GitHubIssue, GitHubPullRequest]
    to: [GitHubIssue, GitHubPullRequest]
    description: "Entity links to another entity"
    direction: "->"
    properties:
      linked_at:
        type: timestamp
        description: "When the link was created"

  DEPENDS_ON:
    from: [GitHubIssue, GitHubPullRequest]
    to: [GitHubIssue, GitHubPullRequest]
    description: "Entity depends on another entity"
    direction: "->"
    properties:
      dependency_type:
        type: string
        description: "Type of dependency"

  SIMILAR_TO:
    from: [GitHubIssue, GitHubPullRequest, GitHubCodeFile]
    to: [GitHubIssue, GitHubPullRequest, GitHubCodeFile]
    description: "Entity is similar to another entity"
    direction: "<->"
    properties:
      similarity_score:
        type: float
        description: "Similarity score"
      similarity_type:
        type: string
        description: "Type of similarity (semantic, structural, etc.)"

  # Workflow relationships (for future GitHub Actions support)
  TRIGGERS_WORKFLOW:
    from: [GitHubCommit, GitHubPullRequest]
    to: GitHubRepository
    description: "Entity triggers a workflow"
    direction: "->"
    properties:
      triggered_at:
        type: timestamp
        description: "When the workflow was triggered"
      workflow_name:
        type: string
        description: "Name of the triggered workflow"

  RUNS_WORKFLOW:
    from: GitHubUser
    to: GitHubRepository
    description: "User runs a workflow"
    direction: "->"
    properties:
      run_at:
        type: timestamp
        description: "When the workflow was run"
      workflow_name:
        type: string
        description: "Name of the workflow"





🎯 Objective
Refactor the existing GitHub connector, particularly the GitHub service file, to make it modular, clean, and easy to understand. The current implementation is overloaded with redundant and scattered logic. The new version should eliminate unnecessary complexity, consolidate related functions, and ensure full alignment with the provided GitHub knowledge graph schema.

🧠 Core Functional Goals
1. Organization-Level Integration (via PAT Token)
When an organization adds GitHub as a source using a Personal Access Token (PAT):

Fetch all GitHub organizations associated with the PAT.

For each organization:

Extract and store complete data as defined in the schema:

GitHubOrganization node

Associated members (GitHubUser) with MEMBER_OF_ORG and ADMIN_OF_ORG relationships

Teams (GitHubTeam) and MEMBER_OF_TEAM + TEAM_HAS_ACCESS

All repositories (GitHubRepository) owned by the organization with ORG_OWNS_REPO

Each repository includes full metadata:

Branches (GitHubBranch)

Commits (GitHubCommit)

Issues (GitHubIssue)

Pull Requests (GitHubPullRequest)

Files & directories (GitHubCodeFile, GitHubDirectory)

Tags (GitHubTag)

Releases (GitHubRelease)

Comments, reviews, and review comments

Relationships such as:

BELONGS_TO, MODIFIES_FILE, CREATES_FILE, CONTAINS_DIRECTORY, etc.

2. User-Level Integration
If an individual user connects their GitHub account:

Fetch the authenticated user profile and store it as a GitHubUser node.

Fetch:

All organizations the user belongs to → Full org-level sync (as above)

All repositories the user directly owns or contributes to → Full repository-level sync

Create appropriate relationships:

OWNS_REPOSITORY, STARS, WATCHES, MEMBER_OF_ORG, CREATES_ISSUE, COMMITS_TO_BRANCH, etc.

🧱 Schema Alignment
All data should be mapped to the graph schema provided, including but not limited to:

Nodes: GitHubRepository, GitHubUser, GitHubOrganization, GitHubCodeFile, GitHubIssue, GitHubPullRequest, GitHubCommit, GitHubBranch, GitHubDirectory, GitHubTeam, GitHubTag, GitHubRelease, GitHubComment, GitHubReview, GitHubMergeCommit, GitHubReviewComment, GitHubTextChunk

Relationships: e.g., ORG_OWNS_REPO, MEMBER_OF_ORG, CREATES_ISSUE, AUTHORS_COMMIT, CONTAINS_FILE, ASSIGNED_TO, MERGES_PULL_REQUEST, TEAM_HAS_ACCESS, BELONGS_TO, CHUNK_OF_FILE, etc.

Ensure that:

All properties are captured as defined

Timestamps are properly parsed and formatted

Identifiers are consistently linked using organisation_id, repository_id, etc.

🔄 Refactor Requirements
Consolidate and deduplicate existing logic across org-level and user-level syncing.

Group functionality into modular classes/services:

GitHubAPIClient: Thin wrapper for calling GitHub REST API

GitHubEntityMapper: Normalize API responses to schema entities

GitHubSyncService:

sync_organization(token)

sync_user(token)

sync_repository(repo)

Ensure clean separation of:

API calls

Data transformation

Database persistence (e.g., Neo4j node/relationship creation)

Simplify the GitHub service file to be easy to follow, extensible, and schema-aligned.

✅ Deliverables
A refactored GitHub service module that:

Supports both org-based and user-based integration

Covers all node types and relationship types in the schema

Removes any unused or redundant functions

Is ready to plug into the existing connector framework